# This file was auto-generated by the Firebase CLI
# https://github.com/firebase/firebase-tools

name: Deploy to Firebase Hosting on PR
"on": pull_request
jobs:
  build_and_preview:
    if: "${{ github.event.pull_request.head.repo.full_name == github.repository }}"
    runs-on: ubuntu-latest
    environment: dev
    steps:
      - uses: actions/checkout@v2
      # - run: 'n'
      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: "${{ secrets.GITHUB_TOKEN }}"
          firebaseServiceAccount: "${{ secrets.FIREBASE_SERVICE_ACCOUNT }}"
          target: dev
          expires: 2d
          projectId: ${{secrets.FIREBASE_PROJECT_ID}}
        env:
          FIREBASE_CLI_PREVIEWS: hostingchannels
