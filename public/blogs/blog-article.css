.cus-header {
  display: grid;
  gap: 20px;
  padding: 2vw 9vw 1vw 9vw;
  grid-template-columns: 7fr 3fr;
}
.cus-heading h1 {
  color: #306fbc;
  margin: 0;
  padding-top:30px;
}

.cus-desc {
  font-style: italic;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  margin-top: -22px;
}

.cus-image img {
  width: 296px;
  max-height: 248px;
}
@media (min-width: 1200px) {
  .cus-heading {
    grid-column: 1;
    grid-row: 1;
  }

  .cus-image {
    grid-column: 2;
    grid-row: 1 / span 2;
  }

  .div-desc {
    grid-column: 1;
    grid-row: 2;
  }
}
@media (max-width: 1200px) {
  .cus-header {
    display: grid;
    grid-template-columns: 60% 40%;
    grid-template-rows: 60% 35%;
    row-gap: 16px;
    padding: 2vw 9vw 0vw 9vw;
  }
  .optimising-revenue-header{
    grid-template-rows: 50% 35%;
  }
  .transforming-revenue-header{
    grid-template-rows: 68% 30%;
  }
  .div-desc {
    grid-column: 1 / span 2;
  }
  .cus-desc {
    margin-top: 0 !important;
    padding-top: 20px;
  }
}

@media (max-width: 600px) {
  .cus-header {
    display: flex;
    flex-direction: column;
  }
  .cus-image {
    order:-1;
    /* grid-row: 1; */
  }
  .cus-heading h1 {
    padding-top: 0;
  }
  .div-desc {
    grid-column: auto !important;
  }
  .cus-desc {
    margin-top: 0 !important;
  }
  .cus-image img {
    height: 250px;
  }
  .autoprice-content {
    padding: 5vw 9vw 6vw;
  }
}
