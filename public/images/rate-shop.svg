<svg width="843" height="809" viewBox="0 0 843 809" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2476_449)">
<path d="M843 0H0V809H843V0Z" fill="white"/>
<path d="M843 0H0V809H843V0Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M527.912 118.434C599.786 128.639 662.37 161.269 709.13 207.845C719.387 198.474 733.849 193.604 748.936 195.727C774.928 199.435 792.851 222.593 788.987 247.47C786.66 262.571 776.834 274.849 763.653 281.33C792.174 335.297 804.161 397.952 794.221 462.343C786.063 515.097 764.136 562.642 732.488 602.132C753.361 615.961 765.436 640.374 761.458 666.068C755.89 702.077 720.897 726.954 683.278 721.619C659.701 718.265 640.724 703.847 631.205 684.658C573.337 714.515 505.273 727.384 435.243 717.448C273.598 694.484 158.8 558.067 165.817 404.997C115.307 380.912 84.3442 327.704 93.064 271.335C103.891 201.306 171.955 152.918 245.103 163.317C261.128 165.6 276.074 170.497 289.509 177.525C355.018 129.507 439.906 105.945 527.912 118.434ZM773.734 133.021C766.744 132.044 760.237 136.645 759.192 143.336C758.165 150.036 762.968 156.263 769.958 157.274C776.948 158.252 783.446 153.651 784.5 146.96C785.553 140.26 780.724 134.033 773.734 133.021ZM165.536 558.614C150.335 556.465 136.215 566.502 133.941 581.064C131.702 595.626 142.186 609.152 157.378 611.334C172.578 613.483 186.699 603.438 188.973 588.876C191.212 574.314 180.736 560.763 165.536 558.614ZM712.002 130.628C698.452 128.698 685.869 137.648 683.849 150.634C681.829 163.612 691.181 175.671 704.731 177.601C718.28 179.539 730.864 170.581 732.883 157.595C734.903 144.642 725.551 132.558 712.002 130.628ZM297.439 73.5767C275.503 70.4755 255.104 84.9869 251.838 105.97C248.598 126.979 263.745 146.521 285.646 149.648C307.581 152.758 327.971 138.271 331.238 117.263C334.478 96.2539 319.339 76.7115 297.439 73.5767ZM88.8578 427.371C52.4946 422.205 18.6341 446.264 13.2337 481.102C7.84198 515.94 32.9476 548.375 69.3107 553.55C105.674 558.724 139.543 534.665 144.935 499.827C150.362 464.989 125.247 432.545 88.8578 427.371Z" fill="#EAF4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M180.05 388.505C178.285 386.381 176.274 384.477 174.053 382.766L177.917 375.089C178.233 374.491 177.978 373.783 177.381 373.454L170.11 369.426C169.513 369.097 168.74 369.266 168.371 369.805L163.322 376.859C160.705 375.907 157.948 375.199 155.191 374.76L154.515 366.241C154.453 365.592 153.882 365.069 153.206 365.069H144.82C144.144 365.069 143.573 365.592 143.52 366.241L142.809 374.76C140.025 375.173 137.294 375.881 134.678 376.859L129.655 369.805C129.26 369.266 128.487 369.097 127.89 369.426L120.619 373.454C120.022 373.783 119.793 374.491 120.083 375.089L123.912 382.817C121.725 384.502 119.714 386.44 117.949 388.53L109.941 384.831C109.309 384.536 108.571 384.78 108.237 385.353L104.031 392.314C103.689 392.887 103.855 393.62 104.426 393.974L111.785 398.82C110.793 401.323 110.055 403.969 109.598 406.606L100.703 407.264C100.027 407.314 99.4824 407.862 99.4824 408.511V416.542C99.4824 417.191 100.027 417.739 100.703 417.789L109.598 418.472C110.02 421.143 110.758 423.756 111.785 426.258L104.426 431.07C103.855 431.458 103.689 432.166 104.031 432.764L108.237 439.733C108.571 440.298 109.309 440.517 109.941 440.247L118.002 436.573C119.767 438.671 121.752 440.576 123.947 442.261L120.11 449.964C119.82 450.562 120.048 451.27 120.645 451.599L127.925 455.627C128.513 455.947 129.286 455.787 129.681 455.239L134.713 448.194C137.321 449.172 140.052 449.854 142.835 450.292L143.547 458.812C143.599 459.461 144.17 459.975 144.855 459.975H153.232C153.917 459.975 154.479 459.461 154.541 458.812L155.217 450.318C158.009 449.905 160.732 449.197 163.375 448.219L168.433 455.273C168.828 455.812 169.566 455.981 170.163 455.652L177.434 451.624C178.031 451.295 178.259 450.587 177.978 449.989L174.114 442.287C176.301 440.601 178.32 438.697 180.077 436.573L188.12 440.247C188.744 440.517 189.481 440.298 189.824 439.733L194.03 432.764C194.373 432.191 194.197 431.458 193.626 431.079L186.268 426.258C187.295 423.756 188.006 421.143 188.463 418.472L197.349 417.789C198.034 417.739 198.57 417.191 198.57 416.542V408.486C198.57 407.828 198.034 407.289 197.349 407.23L188.463 406.581C188.033 403.91 187.295 401.297 186.268 398.769L193.626 393.924C194.197 393.544 194.373 392.837 194.03 392.264L189.824 385.294C189.481 384.721 188.744 384.502 188.12 384.78L180.05 388.505ZM163.858 404.323C168.573 412.16 165.79 422.205 157.579 426.722C149.395 431.239 138.919 428.567 134.195 420.705C129.479 412.868 132.263 402.823 140.473 398.306C148.657 393.789 159.116 396.452 163.858 404.323Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M143.178 375.039C140.166 375.469 137.295 376.202 134.537 377.238L129.374 369.974C129.084 369.561 128.514 369.452 128.066 369.705L120.786 373.733C120.338 373.977 120.163 374.491 120.391 374.955L124.342 382.901C122.015 384.671 119.908 386.71 118.064 388.918L109.792 385.109C109.344 384.89 108.773 385.05 108.519 385.488L104.312 392.458C104.058 392.887 104.172 393.41 104.593 393.705L112.154 398.685C111.074 401.298 110.31 404.045 109.853 406.91L100.703 407.559C100.194 407.584 99.7988 407.997 99.7988 408.486V416.517C99.7988 417.006 100.194 417.41 100.703 417.436L109.88 418.143C110.336 421.009 111.1 423.756 112.18 426.368L104.593 431.323C104.172 431.593 104.058 432.132 104.312 432.571L108.519 439.54C108.773 439.978 109.309 440.138 109.792 439.919L118.09 436.135C119.934 438.368 122.068 440.383 124.369 442.152L120.391 450.074C120.163 450.503 120.338 451.051 120.786 451.296L128.066 455.324C128.514 455.568 129.058 455.459 129.374 455.054L134.573 447.815C137.295 448.843 140.166 449.585 143.152 450.015L143.828 458.779C143.854 459.268 144.285 459.655 144.794 459.655H153.18C153.689 459.655 154.111 459.268 154.146 458.779L154.884 449.989C157.86 449.551 160.706 448.818 163.428 447.815L168.626 455.054C168.916 455.459 169.487 455.568 169.935 455.324L177.206 451.296C177.662 451.051 177.838 450.537 177.609 450.074L173.632 442.152C175.959 440.383 178.066 438.368 179.91 436.135L188.2 439.919C188.656 440.138 189.227 439.978 189.482 439.54L193.688 432.571C193.943 432.132 193.828 431.618 193.407 431.323L185.82 426.368C186.9 423.756 187.664 421.009 188.121 418.143L197.297 417.436C197.806 417.41 198.202 417.006 198.202 416.517V408.486C198.202 407.997 197.806 407.584 197.297 407.559L188.147 406.91C187.69 404.045 186.926 401.298 185.846 398.685L193.407 393.705C193.828 393.435 193.943 392.887 193.688 392.458L189.482 385.488C189.227 385.05 188.683 384.89 188.2 385.109L179.937 388.918C178.092 386.685 175.985 384.671 173.658 382.901L177.609 374.955C177.838 374.516 177.662 373.977 177.206 373.733L169.935 369.705C169.487 369.452 168.942 369.561 168.626 369.974L163.463 377.238C160.732 376.202 157.86 375.469 154.884 375.039L154.146 366.241C154.111 365.752 153.689 365.373 153.18 365.373H144.794C144.285 365.373 143.854 365.752 143.828 366.241L143.178 375.039ZM149.027 395.778C158.686 395.778 166.501 403.261 166.501 412.514C166.501 421.767 158.686 429.25 149.027 429.25C139.367 429.25 131.561 421.767 131.561 412.514C131.561 403.261 139.367 395.778 149.027 395.778Z" fill="#631AEB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M172.605 400.017C168.969 394.008 163.059 389.652 156.078 387.857C149.088 386.062 141.668 386.98 135.39 390.469C129.111 393.949 124.571 399.612 122.692 406.304C120.821 413.003 121.787 420.107 125.423 426.124C129.058 432.133 134.968 436.489 141.958 438.284C148.948 440.088 156.359 439.161 162.638 435.672C168.916 432.192 173.456 426.529 175.335 419.837C177.206 413.113 176.24 406.034 172.605 400.017ZM154.34 394.034C159.626 395.391 164.087 398.66 166.844 403.202C169.566 407.753 170.304 413.087 168.89 418.144C167.467 423.208 164.06 427.481 159.31 430.119C154.568 432.739 149.001 433.439 143.714 432.082C138.437 430.725 133.976 427.456 131.218 422.913C128.488 418.363 127.75 413.028 129.172 407.964C130.595 402.907 134.002 398.635 138.744 395.989C143.486 393.376 149.053 392.702 154.34 394.034Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M149.027 386.795C141.616 386.795 134.907 389.677 130.051 394.329C125.195 398.989 122.183 405.41 122.183 412.514C122.183 419.618 125.195 426.04 130.051 430.692C134.907 435.352 141.616 438.234 149.027 438.234C156.447 438.234 163.147 435.352 168.003 430.692C172.868 426.04 175.871 419.618 175.871 412.514C175.871 405.41 172.868 398.989 168.003 394.329C163.147 389.677 156.447 386.795 149.027 386.795ZM134.195 398.306C138.006 394.657 143.231 392.424 149.027 392.424C154.823 392.424 160.047 394.683 163.858 398.306C167.661 401.955 169.997 406.961 169.997 412.514C169.997 418.068 167.634 423.074 163.858 426.722C160.047 430.371 154.823 432.596 149.027 432.596C143.231 432.596 138.006 430.338 134.195 426.722C130.393 423.074 128.066 418.068 128.066 412.514C128.031 406.961 130.393 401.922 134.195 398.306Z" fill="#9464F1"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M132.184 412.514C132.184 403.615 139.709 396.376 149.026 396.376C158.317 396.376 165.877 403.59 165.877 412.514C165.877 421.413 158.343 428.652 149.026 428.652C139.709 428.652 132.184 421.413 132.184 412.514ZM164.139 404.155C159.309 396.157 148.631 393.41 140.28 398.037C131.929 402.663 129.058 412.894 133.887 420.899C138.717 428.897 149.395 431.644 157.746 427.017C166.106 422.391 168.968 412.16 164.139 404.155Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M729.476 429.849C730.099 429.09 730.837 428.408 731.601 427.809L730.24 425.062C730.125 424.843 730.213 424.599 730.442 424.464L733.023 423.023C733.252 422.913 733.506 422.964 733.647 423.158L735.464 425.661C736.404 425.307 737.37 425.062 738.362 424.927L738.591 421.877C738.617 421.632 738.819 421.464 739.074 421.464H742.059C742.314 421.464 742.516 421.658 742.542 421.877L742.797 424.927C743.789 425.088 744.755 425.332 745.695 425.686L747.486 423.183C747.626 422.989 747.881 422.939 748.109 423.048L750.691 424.489C750.893 424.599 750.981 424.868 750.893 425.088L749.532 427.835C750.322 428.433 751.034 429.116 751.657 429.874L754.529 428.543C754.757 428.433 755.012 428.517 755.152 428.736L756.662 431.214C756.777 431.399 756.715 431.677 756.513 431.812L753.905 433.548C754.274 434.45 754.529 435.377 754.669 436.329L757.857 436.548C758.111 436.574 758.278 436.767 758.278 437.012V439.869C758.278 440.113 758.076 440.298 757.857 440.332L754.669 440.576C754.502 441.529 754.248 442.456 753.879 443.349L756.487 445.068C756.689 445.203 756.741 445.447 756.627 445.666L755.126 448.144C755.012 448.329 754.731 448.414 754.529 448.329L751.657 447.023C751.034 447.756 750.322 448.439 749.532 449.063L750.893 451.818C751.007 452.029 750.919 452.273 750.691 452.417L748.109 453.858C747.881 453.967 747.626 453.908 747.486 453.723L745.695 451.186C744.755 451.54 743.789 451.785 742.797 451.919L742.542 454.97C742.516 455.214 742.314 455.383 742.059 455.383H739.074C738.819 455.383 738.617 455.189 738.591 454.97L738.362 451.919C737.37 451.785 736.404 451.515 735.464 451.186L733.647 453.689C733.506 453.883 733.252 453.933 733.023 453.832L730.442 452.383C730.213 452.273 730.152 452.004 730.24 451.785L731.636 449.037C730.837 448.439 730.125 447.756 729.502 446.998L726.631 448.304C726.402 448.414 726.148 448.329 726.033 448.11L724.532 445.633C724.418 445.422 724.47 445.169 724.672 445.034L727.315 443.324C726.947 442.422 726.692 441.495 726.543 440.543L723.364 440.298C723.109 440.273 722.942 440.088 722.942 439.835V436.978C722.942 436.734 723.136 436.548 723.364 436.515L726.543 436.304C726.692 435.352 726.973 434.425 727.315 433.523L724.699 431.779C724.497 431.644 724.444 431.399 724.558 431.18L726.06 428.711C726.174 428.492 726.464 428.433 726.692 428.517L729.476 429.849ZM735.271 435.487C733.594 438.284 734.586 441.883 737.51 443.484C740.435 445.093 744.184 444.141 745.861 441.335C747.539 438.529 746.546 434.939 743.622 433.338C740.698 431.728 736.94 432.68 735.271 435.487Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M742.63 425.037C743.701 425.197 744.729 425.442 745.695 425.821L747.539 423.234C747.653 423.099 747.855 423.048 747.995 423.124L750.577 424.574C750.753 424.65 750.805 424.843 750.726 425.004L749.304 427.835C750.129 428.459 750.893 429.2 751.543 429.984L754.502 428.627C754.669 428.543 754.871 428.602 754.959 428.762L756.461 431.24C756.548 431.4 756.522 431.594 756.346 431.678L753.651 433.439C754.019 434.366 754.3 435.352 754.476 436.38L757.743 436.624C757.909 436.624 758.05 436.793 758.05 436.953V439.81C758.05 439.978 757.909 440.139 757.743 440.139L754.476 440.383C754.3 441.419 754.046 442.397 753.651 443.324L756.346 445.094C756.487 445.203 756.548 445.389 756.461 445.523L754.959 448.001C754.871 448.17 754.669 448.22 754.502 448.144L751.543 446.779C750.893 447.571 750.129 448.304 749.304 448.928L750.726 451.76C750.805 451.92 750.753 452.113 750.577 452.198L747.995 453.639C747.829 453.723 747.627 453.689 747.539 453.529L745.695 450.942C744.729 451.296 743.701 451.566 742.63 451.734L742.367 454.861C742.367 455.029 742.2 455.164 742.033 455.164H739.047C738.872 455.164 738.705 455.029 738.705 454.861L738.45 451.734C737.396 451.566 736.378 451.321 735.412 450.942L733.568 453.529C733.454 453.664 733.252 453.723 733.111 453.639L730.521 452.198C730.354 452.113 730.292 451.92 730.38 451.76L731.803 448.928C730.977 448.304 730.213 447.571 729.555 446.779L726.604 448.144C726.429 448.22 726.235 448.17 726.148 448.001L724.646 445.523C724.558 445.363 724.585 445.17 724.76 445.094L727.456 443.324C727.061 442.397 726.806 441.419 726.631 440.383L723.364 440.139C723.197 440.139 723.057 439.978 723.057 439.81V436.953C723.057 436.793 723.197 436.624 723.364 436.624L726.631 436.38C726.806 435.352 727.061 434.366 727.456 433.439L724.76 431.678C724.611 431.568 724.558 431.374 724.646 431.24L726.148 428.762C726.235 428.602 726.429 428.543 726.604 428.627L729.555 429.984C730.213 429.2 730.977 428.459 731.803 427.835L730.38 425.004C730.292 424.843 730.354 424.65 730.521 424.574L733.111 423.124C733.278 423.048 733.48 423.074 733.568 423.234L735.412 425.821C736.378 425.467 737.396 425.197 738.477 425.037L738.731 421.902C738.731 421.742 738.907 421.607 739.074 421.607H742.059C742.226 421.607 742.402 421.742 742.402 421.902L742.63 425.037ZM740.549 432.436C737.115 432.436 734.305 435.099 734.305 438.428C734.305 441.714 737.089 444.386 740.549 444.386C743.991 444.386 746.775 441.714 746.775 438.428C746.775 435.099 743.991 432.436 740.549 432.436Z" fill="#631AEB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M732.119 433.962C733.419 431.813 735.526 430.262 738.02 429.605C740.523 428.956 743.166 429.31 745.414 430.532C747.653 431.779 749.278 433.793 749.954 436.195C750.639 438.588 750.27 441.116 748.988 443.265C747.68 445.422 745.581 446.973 743.078 447.622C740.584 448.279 737.941 447.925 735.693 446.695C733.454 445.448 731.83 443.434 731.153 441.04C730.495 438.613 730.837 436.11 732.119 433.962ZM738.653 431.813C736.773 432.301 735.158 433.473 734.192 435.074C733.226 436.709 732.936 438.613 733.454 440.408C733.963 442.203 735.184 443.762 736.861 444.681C738.565 445.608 740.549 445.877 742.428 445.389C744.299 444.9 745.923 443.729 746.889 442.127C747.855 440.493 748.136 438.588 747.627 436.793C747.118 434.998 745.897 433.439 744.22 432.521C742.543 431.594 740.523 431.324 738.653 431.813Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M740.549 429.225C743.192 429.225 745.581 430.261 747.311 431.921C749.049 433.581 750.129 435.865 750.129 438.393C750.129 440.93 749.049 443.214 747.311 444.874C745.581 446.534 743.192 447.57 740.549 447.57C737.906 447.57 735.526 446.534 733.788 444.874C732.058 443.214 730.978 440.93 730.978 438.393C730.978 435.865 732.058 433.581 733.788 431.921C735.491 430.261 737.906 429.225 740.549 429.225ZM745.835 433.337C744.474 432.031 742.595 431.239 740.549 431.239C738.477 431.239 736.606 432.056 735.272 433.337C733.902 434.643 733.085 436.438 733.085 438.393C733.085 440.357 733.937 442.177 735.272 443.458C736.633 444.764 738.503 445.556 740.549 445.556C742.63 445.556 744.501 444.764 745.835 443.458C747.197 442.152 748.022 440.357 748.022 438.393C748.022 436.438 747.197 434.643 745.835 433.337Z" fill="#9464F1"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M746.573 438.428C746.573 435.242 743.877 432.655 740.549 432.655C737.23 432.655 734.534 435.242 734.534 438.428C734.534 441.605 737.23 444.192 740.549 444.192C743.877 444.166 746.573 441.579 746.573 438.428ZM735.158 435.428C736.887 432.571 740.698 431.593 743.675 433.253C746.661 434.914 747.68 438.562 745.95 441.419C744.22 444.276 740.409 445.254 737.423 443.593C734.446 441.933 733.419 438.284 735.158 435.428Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M101.301 516.589C101.924 515.831 102.662 515.148 103.435 514.55L102.074 511.802C101.959 511.583 102.038 511.339 102.267 511.204L104.857 509.763C105.05 509.654 105.34 509.704 105.481 509.898L107.298 512.401C108.238 512.047 109.204 511.802 110.196 511.668L110.425 508.617C110.451 508.373 110.653 508.204 110.908 508.204H113.884C114.139 508.204 114.341 508.398 114.367 508.617L114.622 511.668C115.623 511.828 116.589 512.072 117.52 512.426L119.311 509.923C119.452 509.729 119.715 509.679 119.935 509.788L122.525 511.229C122.753 511.339 122.806 511.609 122.718 511.828L121.357 514.575C122.156 515.173 122.868 515.856 123.491 516.614L126.363 515.308C126.582 515.199 126.846 515.283 126.986 515.502L128.488 517.98C128.602 518.199 128.549 518.443 128.347 518.578L125.73 520.322C126.099 521.216 126.363 522.142 126.503 523.095L129.682 523.314C129.937 523.339 130.112 523.533 130.112 523.777V526.634C130.112 526.879 129.91 527.072 129.682 527.098L126.503 527.342C126.327 528.294 126.073 529.221 125.704 530.115L128.321 531.834C128.514 531.968 128.575 532.213 128.461 532.432L126.96 534.91C126.846 535.129 126.556 535.179 126.363 535.095L123.491 533.789C122.868 534.53 122.156 535.204 121.384 535.837L122.753 538.584C122.868 538.803 122.78 539.047 122.551 539.182L119.97 540.623C119.741 540.733 119.487 540.674 119.346 540.488L117.555 537.96C116.615 538.306 115.649 538.558 114.657 538.693L114.402 541.735C114.367 541.988 114.174 542.148 113.92 542.148H110.934C110.679 542.148 110.477 541.955 110.451 541.735L110.223 538.693C109.23 538.558 108.264 538.28 107.325 537.96L105.507 540.463C105.367 540.648 105.112 540.707 104.884 540.598L102.293 539.157C102.074 539.047 102.012 538.769 102.1 538.558L103.487 535.803C102.697 535.204 101.986 534.53 101.362 533.763L98.4909 535.07C98.2625 535.179 98.0079 535.095 97.8937 534.884L96.3921 532.407C96.278 532.188 96.3307 531.943 96.5326 531.808L99.1758 530.089C98.7982 529.196 98.5435 528.269 98.403 527.317L95.2242 527.072C94.9696 527.039 94.7939 526.853 94.7939 526.609V523.752C94.7939 523.508 94.9959 523.314 95.2242 523.289L98.403 523.069C98.5435 522.117 98.8333 521.19 99.1758 520.289L96.559 518.553C96.357 518.418 96.3043 518.173 96.4185 517.954L97.9201 515.477C98.0342 515.258 98.3152 515.199 98.5435 515.283L101.301 516.589ZM107.096 522.227C105.419 525.025 106.42 528.623 109.344 530.224C112.269 531.834 116.018 530.881 117.695 528.075C119.373 525.269 118.38 521.679 115.447 520.078C112.523 518.468 108.8 519.421 107.096 522.227Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M114.482 511.777C115.562 511.937 116.589 512.181 117.555 512.56L119.399 509.973C119.513 509.838 119.715 509.788 119.856 509.864L122.437 511.313C122.613 511.389 122.666 511.583 122.578 511.743L121.164 514.574C121.981 515.198 122.754 515.939 123.403 516.723L126.363 515.366C126.529 515.282 126.731 515.341 126.81 515.501L128.321 517.979C128.409 518.139 128.374 518.333 128.207 518.417L125.511 520.187C125.88 521.105 126.161 522.091 126.328 523.119L129.594 523.364C129.77 523.397 129.91 523.532 129.91 523.692V526.549C129.91 526.718 129.77 526.878 129.594 526.878L126.328 527.122C126.161 528.159 125.906 529.136 125.511 530.063L128.207 531.833C128.347 531.943 128.409 532.128 128.321 532.271L126.81 534.74C126.731 534.909 126.529 534.959 126.363 534.884L123.403 533.518C122.754 534.311 121.981 535.044 121.164 535.667L122.578 538.499C122.666 538.659 122.613 538.853 122.437 538.937L119.856 540.378C119.68 540.462 119.487 540.429 119.399 540.268L117.555 537.681C116.589 538.035 115.562 538.313 114.482 538.474L114.227 541.6C114.227 541.768 114.06 541.903 113.884 541.903H110.908C110.732 541.903 110.565 541.768 110.565 541.6L110.311 538.474C109.257 538.313 108.238 538.061 107.272 537.681L105.419 540.268C105.305 540.403 105.112 540.462 104.972 540.378L102.381 538.937C102.214 538.853 102.153 538.659 102.241 538.499L103.663 535.667C102.838 535.044 102.074 534.311 101.415 533.518L98.4646 534.884C98.289 534.959 98.0958 534.909 98.008 534.74L96.4976 532.271C96.4186 532.103 96.4449 531.917 96.6118 531.833L99.3164 530.063C98.9125 529.136 98.6578 528.159 98.491 527.122L95.2243 526.878C95.0487 526.853 94.9082 526.718 94.9082 526.549V523.692C94.9082 523.532 95.0487 523.364 95.2243 523.364L98.491 523.119C98.6578 522.091 98.9125 521.105 99.3164 520.187L96.6118 518.417C96.4713 518.307 96.4186 518.114 96.4976 517.979L98.008 515.501C98.0958 515.341 98.289 515.282 98.4646 515.366L101.415 516.723C102.074 515.939 102.838 515.198 103.663 514.574L102.241 511.743C102.153 511.583 102.214 511.389 102.381 511.313L104.972 509.864C105.138 509.788 105.34 509.813 105.419 509.973L107.272 512.56C108.238 512.181 109.257 511.937 110.337 511.777L110.592 508.642C110.618 508.482 110.758 508.347 110.934 508.347H113.92C114.086 508.347 114.253 508.482 114.253 508.642L114.482 511.777ZM112.409 519.175C108.976 519.175 106.157 521.847 106.157 525.133C106.157 528.428 108.941 531.125 112.409 531.125C115.851 531.125 118.635 528.454 118.635 525.133C118.635 521.847 115.851 519.175 112.409 519.175Z" fill="#631AEB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M103.97 520.701C105.278 518.552 107.386 517.001 109.88 516.344C112.382 515.695 115.025 516.049 117.265 517.271C119.513 518.527 121.128 520.541 121.813 522.934C122.498 525.327 122.13 527.855 120.847 530.004C119.539 532.162 117.44 533.712 114.938 534.336C112.435 534.985 109.792 534.639 107.553 533.409C105.305 532.162 103.689 530.148 103.004 527.746C102.328 525.378 102.661 522.85 103.97 520.701ZM110.503 518.552C108.633 519.041 107.008 520.212 106.042 521.813C105.076 523.448 104.795 525.353 105.305 527.148C105.823 528.943 107.043 530.502 108.721 531.42C110.424 532.347 112.409 532.625 114.288 532.128C116.158 531.639 117.783 530.468 118.749 528.867C119.715 527.232 119.996 525.327 119.486 523.532C118.968 521.737 117.748 520.187 116.07 519.26C114.367 518.333 112.382 518.063 110.503 518.552Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M112.408 515.965C115.052 515.965 117.44 517.001 119.17 518.662C120.9 520.322 121.98 522.605 121.98 525.134C121.98 527.67 120.9 529.954 119.17 531.614C117.44 533.274 115.052 534.311 112.408 534.311C109.765 534.311 107.386 533.274 105.647 531.614C103.917 529.954 102.837 527.67 102.837 525.134C102.837 522.605 103.917 520.322 105.647 518.662C107.35 517.001 109.739 515.965 112.408 515.965ZM117.695 520.077C116.334 518.771 114.455 517.979 112.408 517.979C110.336 517.979 108.457 518.771 107.122 520.077C105.761 521.383 104.936 523.178 104.936 525.134C104.936 527.122 105.787 528.917 107.122 530.198C108.492 531.504 110.362 532.297 112.408 532.297C114.481 532.297 116.36 531.479 117.695 530.198C119.056 528.892 119.881 527.097 119.881 525.134C119.881 523.178 119.056 521.383 117.695 520.077Z" fill="#9464F1"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M118.406 525.168C118.406 521.982 115.701 519.395 112.382 519.395C109.054 519.395 106.358 521.982 106.358 525.168C106.358 528.345 109.054 530.932 112.382 530.932C115.701 530.907 118.406 528.32 118.406 525.168ZM106.982 522.168C108.72 519.311 112.523 518.333 115.508 519.994C118.485 521.654 119.512 525.303 117.783 528.159C116.044 531.016 112.242 531.994 109.256 530.334C106.305 528.674 105.278 525.025 106.982 522.168Z" fill="#360E93"/>
<path d="M797.17 615.009C797.17 615.102 797.038 615.203 796.775 615.295C796.512 615.396 796.125 615.489 795.598 615.59C795.071 615.683 794.422 615.784 793.64 615.877C792.85 615.97 791.937 616.071 790.9 616.163C789.855 616.256 788.687 616.357 787.388 616.45C786.088 616.543 784.666 616.635 783.111 616.728C781.566 616.821 779.889 616.913 778.089 617.006C776.288 617.099 774.365 617.191 772.319 617.276C770.282 617.368 768.113 617.453 765.83 617.545C763.547 617.63 761.149 617.722 758.629 617.807C756.109 617.891 753.475 617.975 750.735 618.059C747.986 618.144 745.124 618.22 742.156 618.304C739.188 618.388 736.114 618.464 732.935 618.54C729.748 618.616 726.464 618.7 723.074 618.767C719.684 618.843 716.198 618.919 712.615 618.995C709.024 619.062 705.345 619.13 701.569 619.206C697.784 619.273 693.92 619.34 689.96 619.399C686 619.467 681.96 619.534 677.833 619.593C673.697 619.652 669.491 619.711 665.197 619.77C660.911 619.829 656.547 619.888 652.104 619.939C647.661 619.998 643.147 620.048 638.563 620.099C633.988 620.149 629.334 620.191 624.627 620.242C619.912 620.284 615.144 620.326 610.314 620.368C605.484 620.411 600.602 620.453 595.667 620.486C590.732 620.529 585.753 620.562 580.721 620.596C575.689 620.63 570.623 620.655 565.512 620.689C560.393 620.714 555.247 620.739 550.066 620.765C544.885 620.79 539.678 620.807 534.435 620.824C529.193 620.849 523.933 620.866 518.646 620.874C513.369 620.891 508.065 620.899 502.752 620.908C497.431 620.925 492.11 620.925 486.771 620.933C481.44 620.942 476.101 620.942 470.754 620.942C465.415 620.942 460.076 620.942 454.737 620.933C449.406 620.925 444.076 620.925 438.763 620.908C433.442 620.899 428.147 620.891 422.861 620.874C417.574 620.866 412.314 620.849 407.072 620.824C401.838 620.807 396.622 620.79 391.441 620.765C386.26 620.739 381.115 620.714 376.004 620.689C370.893 620.655 365.818 620.63 360.786 620.596C355.763 620.562 350.775 620.529 345.84 620.486C340.905 620.453 336.023 620.411 331.193 620.368C326.363 620.326 321.595 620.284 316.888 620.242C312.173 620.191 307.528 620.149 302.944 620.099C298.36 620.048 293.846 619.998 289.412 619.939C284.969 619.888 280.604 619.829 276.31 619.77C272.016 619.711 267.81 619.652 263.683 619.593C259.547 619.534 255.508 619.467 251.547 619.399C247.587 619.34 243.723 619.273 239.947 619.206C236.171 619.138 232.483 619.062 228.9 618.995C225.309 618.919 221.823 618.843 218.433 618.767C215.044 618.7 211.759 618.616 208.581 618.54C205.393 618.464 202.32 618.388 199.351 618.304C196.383 618.22 193.529 618.144 190.781 618.059C188.032 617.975 185.398 617.891 182.887 617.807C180.366 617.722 177.96 617.63 175.677 617.545C173.394 617.453 171.234 617.368 169.188 617.276C167.142 617.191 165.219 617.099 163.419 617.006C161.618 616.913 159.95 616.821 158.396 616.728C156.841 616.635 155.419 616.543 154.119 616.45C152.828 616.357 151.652 616.256 150.616 616.163C149.571 616.071 148.657 615.978 147.876 615.877C147.085 615.784 146.436 615.683 145.909 615.59C145.391 615.489 144.996 615.396 144.732 615.295C144.469 615.203 144.337 615.102 144.337 615.009C144.337 614.908 144.469 614.815 144.732 614.714C144.996 614.621 145.391 614.52 145.909 614.427C146.436 614.326 147.085 614.234 147.876 614.132C148.657 614.04 149.571 613.947 150.616 613.846C151.652 613.753 152.828 613.661 154.119 613.568C155.419 613.475 156.841 613.374 158.396 613.281C159.95 613.189 161.618 613.096 163.419 613.012C165.219 612.919 167.142 612.826 169.188 612.734C171.234 612.649 173.394 612.557 175.677 612.472C177.96 612.38 180.366 612.295 182.887 612.211C185.398 612.127 188.032 612.043 190.781 611.958C193.529 611.874 196.383 611.79 199.351 611.714C202.32 611.63 205.393 611.554 208.581 611.469C211.759 611.394 215.044 611.318 218.433 611.242C221.823 611.166 225.309 611.099 228.9 611.023C232.483 610.947 236.171 610.88 239.947 610.812C243.723 610.745 247.587 610.677 251.547 610.61C255.508 610.542 259.547 610.483 263.683 610.425C267.81 610.357 272.016 610.298 276.31 610.239C280.604 610.18 284.969 610.13 289.412 610.071C293.846 610.02 298.36 609.969 302.944 609.919C307.528 609.868 312.173 609.818 316.888 609.776C321.595 609.725 326.363 609.683 331.193 609.641C336.023 609.599 340.905 609.565 345.84 609.523C350.775 609.489 355.763 609.455 360.786 609.422C365.818 609.388 370.893 609.354 376.004 609.329C381.115 609.304 386.26 609.278 391.441 609.253C396.622 609.228 401.838 609.211 407.072 609.186C412.314 609.169 417.574 609.152 422.861 609.135C428.147 609.127 433.442 609.11 438.763 609.101C444.076 609.093 449.406 609.085 454.737 609.085C460.076 609.076 465.415 609.076 470.754 609.076C476.101 609.076 481.44 609.076 486.771 609.085C492.11 609.085 497.431 609.093 502.752 609.101C508.065 609.11 513.369 609.127 518.646 609.135C523.933 609.152 529.193 609.169 534.435 609.186C539.678 609.211 544.885 609.228 550.066 609.253C555.247 609.278 560.393 609.304 565.512 609.329C570.623 609.354 575.689 609.388 580.721 609.422C585.753 609.455 590.732 609.489 595.667 609.523C600.602 609.565 605.484 609.599 610.314 609.641C615.144 609.683 619.912 609.725 624.627 609.776C629.334 609.818 633.988 609.868 638.563 609.919C643.147 609.969 647.661 610.02 652.104 610.071C656.547 610.13 660.911 610.18 665.197 610.239C669.491 610.298 673.697 610.357 677.833 610.425C681.96 610.483 686.008 610.542 689.96 610.61C693.92 610.677 697.784 610.745 701.569 610.812C705.345 610.88 709.024 610.947 712.615 611.023C716.198 611.099 719.684 611.166 723.074 611.242C726.464 611.318 729.748 611.394 732.935 611.469C736.114 611.554 739.188 611.63 742.156 611.714C745.124 611.79 747.986 611.874 750.726 611.958C753.475 612.043 756.109 612.127 758.629 612.211C761.149 612.295 763.547 612.38 765.83 612.472C768.113 612.557 770.282 612.649 772.319 612.734C774.365 612.826 776.288 612.919 778.089 613.012C779.889 613.096 781.566 613.189 783.111 613.281C784.666 613.374 786.088 613.475 787.388 613.568C788.687 613.661 789.855 613.753 790.9 613.846C791.937 613.947 792.85 614.04 793.64 614.132C794.422 614.234 795.071 614.326 795.598 614.427C796.125 614.52 796.512 614.621 796.775 614.714C797.038 614.815 797.17 614.908 797.17 615.009Z" fill="#B6D8F8"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M505.414 586.398L492.971 486.268H492.233C461.016 486.268 434.532 486.268 403.341 486.268H402.603L390.16 586.398C387.543 595.053 349.793 601.618 352.26 611.823C352.743 613.778 360.418 614.385 361.867 614.545C369.024 615.337 376.33 615.716 383.513 616.07C404.965 617.073 426.497 617.376 447.976 617.427C469.393 617.486 490.925 617.292 512.316 616.34C519.508 616.011 526.832 615.632 533.997 614.789C535.446 614.629 543.595 613.946 543.709 611.629C544.104 600.471 510.472 596.469 505.414 586.398Z" fill="#3090D7"/>
<mask id="mask0_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="352" y="500" width="190" height="113">
<path d="M352.936 500.501H541.768V612.682H352.936V500.501Z" fill="white"/>
</mask>
<g mask="url(#mask0_2476_449)">
<mask id="mask1_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="354" y="500" width="188" height="111">
<path d="M541.267 606.539C538.053 612.287 357.405 611.52 354.622 606.539C354.191 598.239 384.733 592.744 389.536 584.275L401.724 500.805C429.28 500.805 466.293 500.805 493.848 500.805L506.037 584.275C510.84 592.744 541.689 598.272 541.267 606.539Z" fill="white"/>
</mask>
<g mask="url(#mask1_2476_449)">
<path d="M354.191 500.805V612.287H541.689V500.805H354.191Z" fill="url(#paint0_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M717.173 191.27H178.689C169.654 191.27 162.269 198.34 162.269 206.994V508.372C162.269 517.027 169.654 524.106 178.689 524.106H717.173C726.209 524.106 733.594 517.027 733.594 508.372V206.969C733.594 198.315 726.209 191.27 717.173 191.27Z" fill="#1369B0"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M717.173 193.359H178.689C170.874 193.359 164.455 199.486 164.455 206.994V508.372C164.455 515.855 170.848 522.007 178.689 522.007H717.173C724.98 522.007 731.408 515.881 731.408 508.372V206.969C731.408 199.486 725.015 193.359 717.173 193.359Z" fill="#3EA4EB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M590.495 193.359H178.689C170.874 193.359 164.455 199.486 164.455 206.994V508.372C164.455 515.855 170.848 522.007 178.689 522.007H349.221L590.495 193.359Z" fill="#4DABED"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M181.412 207.188H714.469V481.751H181.412V207.188Z" fill="#A7D2F6"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M182.668 208.359H713.222V480.529H182.668V208.359Z" fill="#1369B0"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M579.449 208.385L379.649 480.554H182.668V208.385H579.449Z" fill="#217DC4"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M691.603 219.652H204.252C198.825 219.652 194.399 223.9 194.399 229.099V248.153H701.438V229.099C701.464 223.9 697.029 219.652 691.603 219.652Z" fill="#C4DFFA"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M701.464 248.152H194.426V459.52C194.426 464.686 198.86 468.959 204.287 468.959H691.629C697.056 468.959 701.49 464.712 701.49 459.52V248.152H701.464Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M275.082 230.136C272.887 226.512 268.092 225.29 264.307 227.355C260.531 229.453 259.258 234.079 261.409 237.669C263.605 241.293 268.399 242.515 272.184 240.45C275.96 238.352 277.268 233.751 275.082 230.136Z" fill="#3EA4EB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M233.801 230.136C231.614 226.512 226.811 225.29 223.035 227.355C219.25 229.453 217.977 234.079 220.137 237.669C222.323 241.293 227.153 242.515 230.903 240.45C234.679 238.352 235.987 233.751 233.801 230.136Z" fill="#FF5668"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M254.454 230.136C252.267 226.512 247.438 225.29 243.688 227.355C239.903 229.453 238.63 234.079 240.79 237.669C242.977 241.293 247.78 242.515 251.556 240.45C255.332 238.352 256.614 233.751 254.454 230.136Z" fill="#FF7C11"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M560.068 320.929H681.6C683.901 320.929 685.78 322.724 685.78 324.932V448.001C685.78 450.209 683.901 452.004 681.6 452.004H560.068C557.767 452.004 555.897 450.209 555.897 448.001V324.932C555.87 322.724 557.767 320.929 560.068 320.929Z" fill="#EAF4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M641.488 330.999H664.389C666.69 330.999 668.569 332.794 668.569 335.002V353.263C668.569 355.463 666.69 357.266 664.389 357.266H641.488C639.187 357.266 637.316 355.463 637.316 353.263V335.002C637.316 332.794 639.187 330.999 641.488 330.999Z" fill="#C4DFFA"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M641.488 369.536H664.389C666.69 369.536 668.569 371.331 668.569 373.539V391.8C668.569 394.008 666.69 395.803 664.389 395.803H641.488C639.187 395.803 637.316 394.008 637.316 391.8V373.539C637.316 371.331 639.187 369.536 641.488 369.536Z" fill="#C4DFFA"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M641.488 408.073H664.389C666.69 408.073 668.569 409.877 668.569 412.076V430.338C668.569 432.545 666.69 434.34 664.389 434.34H641.488C639.187 434.34 637.316 432.545 637.316 430.338V412.076C637.316 409.877 639.187 408.073 641.488 408.073Z" fill="#C4DFFA"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M567.058 333.553H603.711V339.241H567.058V333.553Z" fill="#9464F1"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M567.058 345.611H620.07V348.468H567.058V345.611Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M567.058 351.898H608.338V354.755H567.058V351.898Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M567.058 372.098H594.613V377.786H567.058V372.098Z" fill="#9464F1"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M567.058 384.148H620.07V387.014H567.058V384.148Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M567.058 390.443H581.266V393.3H567.058V390.443Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M567.058 410.635H615.416V416.323H567.058V410.635Z" fill="#9464F1"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M567.058 422.694H620.07V425.551H567.058V422.694Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M567.058 428.98H606.125V431.837H567.058V428.98Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M683.418 528.049C685.579 525.462 688.055 523.119 690.724 521.029L686.009 511.633C685.64 510.9 685.921 510.032 686.659 509.619L695.554 504.723C696.292 504.319 697.205 504.504 697.688 505.187L703.879 513.816C707.084 512.644 710.438 511.776 713.819 511.254L714.645 500.83C714.697 500.012 715.408 499.389 716.26 499.389H726.517C727.369 499.389 728.053 500.012 728.141 500.804L728.993 511.229C732.4 511.743 735.72 512.619 738.933 513.791L745.072 505.161C745.555 504.479 746.494 504.285 747.197 504.698L756.092 509.594C756.83 510.007 757.111 510.875 756.742 511.608L752.061 521.055C754.731 523.119 757.172 525.462 759.359 528.024L769.159 523.507C769.922 523.153 770.836 523.423 771.266 524.131L776.377 532.65C776.798 533.358 776.579 534.226 775.894 534.69L766.884 540.622C768.105 543.698 769.018 546.909 769.554 550.145L780.442 550.937C781.294 550.996 781.944 551.67 781.944 552.488V562.314C781.944 563.131 781.294 563.788 780.469 563.864L769.589 564.682C769.044 567.951 768.14 571.137 766.919 574.204L775.92 580.086C776.631 580.55 776.833 581.452 776.403 582.126L771.292 590.646C770.862 591.353 769.958 591.623 769.185 591.278L759.332 586.786C757.172 589.339 754.757 591.682 752.088 593.721L756.777 603.168C757.146 603.901 756.856 604.769 756.118 605.182L747.232 610.078C746.494 610.491 745.555 610.272 745.098 609.615L738.96 600.994C735.755 602.157 732.427 603.033 729.019 603.547L728.168 613.972C728.106 614.789 727.395 615.387 726.543 615.387H716.295C715.435 615.387 714.759 614.764 714.671 613.946L713.845 603.547C710.438 603.033 707.119 602.191 703.905 600.994L697.714 609.59C697.231 610.247 696.318 610.458 695.58 610.053L686.694 605.157C685.947 604.744 685.666 603.876 686.035 603.143L690.751 593.721C688.081 591.657 685.614 589.314 683.454 586.752L673.592 591.244C672.828 591.598 671.915 591.328 671.493 590.62L666.374 582.1C665.952 581.393 666.181 580.499 666.857 580.061L675.867 574.179C674.646 571.103 673.733 567.918 673.197 564.656L662.317 563.839C661.465 563.788 660.842 563.106 660.842 562.288V552.462C660.842 551.645 661.492 550.996 662.343 550.912L673.197 550.12C673.733 546.858 674.62 543.673 675.867 540.597L666.892 534.664C666.207 534.201 665.979 533.325 666.409 532.625L671.52 524.105C671.941 523.397 672.855 523.119 673.619 523.473L683.418 528.049ZM703.22 547.372C697.46 556.954 700.867 569.224 710.895 574.777C720.897 580.306 733.709 577.036 739.504 567.429C745.265 557.847 741.858 545.578 731.829 540.024C721.801 534.496 709.016 537.766 703.22 547.372Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M728.51 511.583C732.172 512.097 735.693 513.024 739.048 514.28L745.379 505.406C745.722 504.918 746.433 504.749 746.977 505.052L755.864 509.949C756.408 510.252 756.602 510.901 756.347 511.423L751.517 521.14C754.362 523.314 756.944 525.791 759.192 528.513L769.299 523.862C769.87 523.584 770.555 523.803 770.862 524.317L775.982 532.837C776.289 533.359 776.149 534.008 775.613 534.362L766.349 540.463C767.657 543.648 768.623 547.019 769.159 550.533L780.355 551.351C780.978 551.401 781.461 551.89 781.461 552.488V562.314C781.461 562.913 780.978 563.402 780.355 563.461L769.159 564.303C768.623 567.784 767.657 571.163 766.349 574.374L775.613 580.441C776.122 580.77 776.289 581.452 775.982 581.966L770.862 590.486C770.555 591 769.87 591.194 769.334 590.95L759.157 586.323C756.891 589.045 754.301 591.523 751.465 593.672L756.321 603.354C756.602 603.902 756.373 604.559 755.838 604.854L746.942 609.751C746.407 610.054 745.722 609.919 745.353 609.397L738.986 600.531C735.667 601.779 732.146 602.705 728.475 603.22L727.623 613.947C727.571 614.546 727.061 615.009 726.438 615.009H716.182C715.549 615.009 715.04 614.546 714.987 613.947L714.1 603.22C710.465 602.705 706.97 601.804 703.651 600.557L697.284 609.43C696.942 609.919 696.239 610.079 695.695 609.784L686.799 604.88C686.264 604.585 686.062 603.927 686.316 603.388L691.181 593.697C688.336 591.523 685.754 589.07 683.48 586.349L673.311 590.975C672.741 591.219 672.091 591.026 671.775 590.511L666.664 581.992C666.348 581.478 666.524 580.82 667.033 580.466L676.288 574.399C674.989 571.213 674.023 567.843 673.478 564.329L662.291 563.486C661.659 563.435 661.176 562.938 661.176 562.34V552.514C661.176 551.915 661.659 551.427 662.291 551.376L673.478 550.559C674.023 547.045 674.954 543.699 676.288 540.488L667.033 534.387C666.524 534.067 666.348 533.384 666.664 532.87L671.775 524.35C672.091 523.828 672.767 523.643 673.338 523.887L683.454 528.539C685.728 525.817 688.283 523.339 691.12 521.165L686.29 511.423C686.035 510.876 686.237 510.252 686.773 509.949L695.669 505.052C696.204 504.749 696.889 504.884 697.258 505.406L703.589 514.28C706.917 513.024 710.439 512.097 714.1 511.583L714.987 500.856C715.04 500.257 715.549 499.794 716.182 499.794H726.438C727.061 499.794 727.571 500.257 727.623 500.856L728.51 511.583ZM721.345 536.949C709.56 536.949 699.989 546.118 699.989 557.418C699.989 568.711 709.56 577.879 721.345 577.879C733.138 577.879 742.71 568.711 742.71 557.418C742.71 546.092 733.138 536.949 721.345 536.949Z" fill="#631AEB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M692.516 542.123C696.942 534.775 704.186 529.44 712.739 527.258C721.292 525.058 730.355 526.196 738.021 530.443C745.695 534.69 751.263 541.634 753.537 549.825C755.838 558.016 754.644 566.696 750.209 574.045C745.783 581.393 738.539 586.728 729.986 588.91C721.433 591.11 712.371 589.964 704.705 585.725C697.03 581.477 691.462 574.534 689.188 566.342C686.887 558.151 688.082 549.471 692.516 542.123ZM714.847 534.825C708.366 536.485 702.913 540.488 699.585 546.042C696.239 551.595 695.353 558.126 697.091 564.328C698.821 570.531 702.992 575.764 708.788 578.941C714.583 582.16 721.407 583.003 727.878 581.343C734.333 579.683 739.812 575.68 743.14 570.126C746.486 564.573 747.373 558.042 745.634 551.839C743.904 545.662 739.733 540.404 733.937 537.218C728.142 534.008 721.319 533.165 714.847 534.825Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M721.345 525.951C730.407 525.951 738.618 529.465 744.562 535.154C750.499 540.842 754.16 548.704 754.16 557.393C754.16 566.073 750.499 573.935 744.562 579.623C738.618 585.312 730.407 588.826 721.345 588.826C712.283 588.826 704.072 585.312 698.136 579.623C692.2 573.935 688.538 566.073 688.538 557.393C688.538 548.704 692.2 540.842 698.136 535.154C704.107 529.491 712.283 525.951 721.345 525.951ZM739.504 540.024C734.868 535.592 728.449 532.836 721.38 532.836C714.302 532.836 707.883 535.592 703.255 540.024C698.619 544.457 695.756 550.609 695.756 557.393C695.756 564.168 698.619 570.32 703.255 574.752C707.883 579.193 714.302 581.941 721.38 581.941C728.449 581.941 734.868 579.193 739.504 574.752C744.132 570.32 747.004 564.168 747.004 557.393C747.004 550.609 744.132 544.457 739.504 540.024Z" fill="#9464F1"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M741.945 557.392C741.945 546.504 732.707 537.656 721.344 537.656C709.981 537.656 700.752 546.504 700.752 557.392C700.752 568.272 709.981 577.12 721.344 577.12C732.742 577.12 741.945 568.305 741.945 557.392ZM702.851 547.153C708.761 537.353 721.801 534.007 732.031 539.67C742.252 545.333 745.747 557.822 739.837 567.623C733.936 577.39 720.896 580.769 710.666 575.106C700.471 569.477 696.976 556.954 702.851 547.153Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M177.522 552.952C178.628 551.646 179.884 550.449 181.245 549.387L178.857 544.626C178.655 544.247 178.83 543.808 179.199 543.615L183.713 541.112C184.082 540.893 184.565 541.002 184.793 541.356L187.945 545.738C189.57 545.14 191.273 544.702 193.003 544.432L193.434 539.157C193.46 538.744 193.829 538.415 194.259 538.415H199.458C199.879 538.415 200.248 538.744 200.274 539.157L200.704 544.432C202.434 544.702 204.112 545.14 205.762 545.738L208.889 541.356C209.143 541.027 209.6 540.926 209.969 541.112L214.482 543.615C214.851 543.834 214.992 544.272 214.825 544.626L212.436 549.412C213.797 550.474 215.053 551.646 216.133 552.952L221.103 550.668C221.498 550.474 221.955 550.643 222.148 550.997L224.765 555.32C224.993 555.674 224.879 556.137 224.51 556.356L219.935 559.373C220.559 560.924 221.016 562.559 221.297 564.219L226.811 564.632C227.241 564.657 227.575 565.011 227.575 565.415V570.396C227.575 570.809 227.241 571.163 226.811 571.188L221.297 571.592C221.016 573.253 220.559 574.862 219.935 576.438L224.51 579.438C224.853 579.682 224.967 580.112 224.765 580.466L222.148 584.798C221.929 585.152 221.472 585.286 221.103 585.126L216.098 582.834C215.018 584.14 213.771 585.312 212.41 586.374L214.798 591.169C214.965 591.548 214.825 591.978 214.456 592.171L209.934 594.674C209.565 594.893 209.082 594.784 208.853 594.43L205.736 590.048C204.112 590.646 202.408 591.084 200.678 591.354L200.248 596.663C200.222 597.068 199.853 597.396 199.422 597.396H194.224C193.802 597.396 193.434 597.068 193.407 596.663L192.977 591.379C191.247 591.11 189.544 590.671 187.919 590.073L184.767 594.455C184.512 594.784 184.055 594.893 183.686 594.7L179.173 592.197C178.804 591.978 178.655 591.548 178.83 591.194L181.219 586.399C179.849 585.337 178.602 584.174 177.522 582.868L172.525 585.152C172.122 585.312 171.674 585.177 171.472 584.823L168.855 580.5C168.627 580.146 168.741 579.682 169.11 579.463L173.685 576.472C173.061 574.913 172.604 573.286 172.323 571.626L166.783 571.213C166.361 571.188 166.019 570.834 166.019 570.43V565.449C166.019 565.036 166.361 564.682 166.783 564.657L172.297 564.244C172.578 562.584 173.035 560.957 173.658 559.407L169.083 556.382C168.741 556.137 168.627 555.699 168.829 555.345L171.445 551.022C171.674 550.668 172.122 550.533 172.49 550.693L177.522 552.952ZM187.577 562.752C184.652 567.623 186.382 573.851 191.475 576.657C196.56 579.463 203.058 577.803 205.991 572.932C208.915 568.062 207.185 561.825 202.092 559.019C197.043 556.221 190.536 557.882 187.577 562.752Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M200.424 544.566C202.294 544.844 204.086 545.308 205.789 545.931L209.003 541.415C209.17 541.17 209.539 541.086 209.793 541.246L214.316 543.749C214.597 543.917 214.684 544.246 214.57 544.516L212.12 549.471C213.569 550.583 214.878 551.839 216.019 553.196L221.156 550.828C221.446 550.693 221.78 550.802 221.955 551.072L224.563 555.403C224.739 555.673 224.651 556.002 224.397 556.162L219.707 559.263C220.366 560.898 220.849 562.617 221.13 564.387L226.811 564.791C227.128 564.817 227.382 565.061 227.382 565.364V570.345C227.382 570.64 227.128 570.884 226.811 570.918L221.13 571.348C220.849 573.117 220.366 574.836 219.707 576.471L224.423 579.547C224.678 579.707 224.765 580.061 224.599 580.306L221.982 584.629C221.815 584.907 221.472 584.983 221.183 584.881L216.019 582.539C214.851 583.921 213.543 585.176 212.12 586.263L214.597 591.193C214.737 591.463 214.623 591.792 214.342 591.952L209.82 594.455C209.539 594.623 209.196 594.539 209.029 594.294L205.789 589.803C204.086 590.426 202.294 590.89 200.45 591.168L200.02 596.612C199.993 596.907 199.739 597.151 199.423 597.151H194.224C193.917 597.151 193.662 596.907 193.627 596.612L193.179 591.168C191.326 590.89 189.544 590.426 187.867 589.803L184.626 594.294C184.451 594.539 184.082 594.623 183.827 594.455L179.314 591.952C179.033 591.792 178.945 591.463 179.059 591.193L181.526 586.263C180.078 585.176 178.769 583.895 177.636 582.539L172.464 584.881C172.183 585.016 171.841 584.907 171.674 584.629L169.057 580.306C168.89 580.036 168.969 579.707 169.224 579.547L173.939 576.471C173.263 574.836 172.78 573.117 172.526 571.348L166.844 570.918C166.528 570.884 166.273 570.64 166.273 570.345V565.364C166.273 565.061 166.528 564.817 166.844 564.791L172.526 564.387C172.807 562.617 173.29 560.898 173.939 559.263L169.259 556.162C169.004 556.002 168.917 555.648 169.083 555.403L171.7 551.072C171.867 550.802 172.21 550.718 172.491 550.828L177.636 553.196C178.769 551.805 180.104 550.558 181.526 549.471L179.085 544.516C178.945 544.246 179.059 543.917 179.34 543.749L183.862 541.246C184.143 541.086 184.486 541.17 184.653 541.415L187.867 545.931C189.57 545.308 191.361 544.811 193.206 544.566L193.662 539.122C193.688 538.827 193.943 538.583 194.259 538.583H199.458C199.765 538.583 200.02 538.827 200.055 539.122L200.424 544.566ZM196.78 557.468C190.791 557.468 185.935 562.12 185.935 567.867C185.935 573.606 190.791 578.266 196.78 578.266C202.777 578.266 207.633 573.606 207.633 567.867C207.633 562.12 202.777 557.468 196.78 557.468Z" fill="#631AEB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M182.15 560.081C184.398 556.356 188.095 553.659 192.441 552.513C196.779 551.375 201.389 551.974 205.279 554.123C209.17 556.272 211.98 559.811 213.147 563.974C214.315 568.137 213.718 572.544 211.47 576.278C209.222 580.002 205.534 582.724 201.187 583.845C196.841 584.957 192.239 584.384 188.349 582.21C184.45 580.061 181.64 576.522 180.446 572.359C179.287 568.221 179.884 563.839 182.15 560.081ZM193.486 556.381C190.193 557.224 187.436 559.263 185.732 562.069C184.029 564.876 183.572 568.221 184.45 571.348C185.337 574.508 187.462 577.145 190.395 578.78C193.346 580.415 196.814 580.853 200.081 580.002C203.374 579.16 206.131 577.12 207.835 574.314C209.538 571.491 209.995 568.162 209.117 565.036C208.23 561.876 206.105 559.238 203.172 557.603C200.248 555.977 196.779 555.538 193.486 556.381Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M196.779 551.89C201.389 551.89 205.56 553.685 208.572 556.575C211.584 559.457 213.463 563.46 213.463 567.867C213.463 572.275 211.584 576.278 208.572 579.16C205.56 582.05 201.389 583.845 196.779 583.845C192.178 583.845 188.007 582.05 184.995 579.16C181.983 576.278 180.104 572.275 180.104 567.867C180.104 563.46 181.983 559.457 184.995 556.575C188.033 553.659 192.178 551.89 196.779 551.89ZM206.017 559.019C203.655 556.761 200.388 555.378 196.814 555.378C193.205 555.378 189.965 556.761 187.611 559.019C185.249 561.277 183.8 564.412 183.8 567.842C183.8 571.297 185.249 574.398 187.611 576.657C189.965 578.915 193.231 580.306 196.814 580.306C200.423 580.306 203.655 578.915 206.017 576.657C208.37 574.398 209.819 571.272 209.819 567.842C209.819 564.412 208.37 561.277 206.017 559.019Z" fill="#9464F1"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M207.263 567.842C207.263 562.314 202.574 557.822 196.814 557.822C191.045 557.822 186.355 562.314 186.355 567.842C186.355 573.362 191.045 577.853 196.814 577.853C202.574 577.879 207.263 573.396 207.263 567.842ZM187.409 562.668C190.395 557.687 197.042 556.002 202.241 558.859C207.439 561.716 209.195 568.086 206.219 573.067C203.233 578.047 196.585 579.733 191.387 576.876C186.188 573.994 184.397 567.623 187.409 562.668Z" fill="#360E93"/>
<mask id="mask2_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="759" y="453" width="74" height="72">
<path d="M759.824 453.04H832.287V524.232H759.824V453.04Z" fill="white"/>
</mask>
<g mask="url(#mask2_2476_449)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M818.878 471.816C817.596 470.29 816.147 468.908 814.558 467.678L817.341 462.133C817.57 461.695 817.394 461.181 816.972 460.928L811.712 458.021C811.291 457.776 810.746 457.911 810.465 458.29L806.804 463.38C804.898 462.672 802.94 462.184 800.92 461.889L800.437 455.737C800.411 455.24 799.981 454.886 799.498 454.886H793.447C792.964 454.886 792.543 455.273 792.508 455.737L791.999 461.889C789.988 462.184 788.021 462.698 786.124 463.406L782.48 458.316C782.199 457.911 781.663 457.802 781.233 458.046L775.982 460.962C775.551 461.206 775.384 461.72 775.578 462.133L778.361 467.712C776.772 468.934 775.323 470.324 774.05 471.816L768.254 469.153C767.797 468.934 767.253 469.094 766.998 469.507L763.96 474.538C763.705 474.951 763.846 475.465 764.241 475.734L769.554 479.248C768.816 481.069 768.307 482.948 767.999 484.886L761.571 485.35C761.062 485.375 760.693 485.779 760.693 486.243V492.041C760.693 492.504 761.089 492.909 761.571 492.942L767.999 493.431C768.307 495.361 768.851 497.24 769.589 499.061L764.276 502.549C763.846 502.819 763.732 503.333 763.986 503.746L767.033 508.777C767.288 509.19 767.824 509.35 768.254 509.165L774.076 506.493C775.349 507.993 776.772 509.375 778.361 510.606L775.613 516.184C775.384 516.614 775.551 517.137 776.008 517.356L781.259 520.263C781.689 520.507 782.26 520.373 782.515 519.993L786.15 514.903C788.056 515.611 790.014 516.1 792.025 516.429L792.543 522.581C792.569 523.044 792.991 523.423 793.474 523.423H799.533C800.042 523.423 800.437 523.044 800.464 522.581L800.947 516.429C802.966 516.125 804.925 515.611 806.83 514.903L810.492 519.993C810.782 520.373 811.317 520.507 811.748 520.263L816.999 517.356C817.429 517.111 817.596 516.589 817.368 516.159L814.584 510.606C816.173 509.375 817.622 507.993 818.904 506.493L824.726 509.131C825.183 509.325 825.719 509.19 825.947 508.752L828.985 503.721C829.24 503.308 829.099 502.768 828.704 502.516L823.391 499.035C824.129 497.215 824.638 495.336 824.981 493.406L831.4 492.909C831.883 492.883 832.287 492.479 832.287 492.015V486.218C832.287 485.754 831.883 485.35 831.4 485.316L824.981 484.852C824.674 482.923 824.129 481.043 823.391 479.223L828.704 475.709C829.099 475.439 829.24 474.925 828.985 474.512L825.947 469.481C825.692 469.069 825.157 468.908 824.7 469.128L818.878 471.816ZM807.173 483.251C810.58 488.914 808.56 496.153 802.65 499.44C796.74 502.709 789.189 500.78 785.746 495.117C782.339 489.454 784.359 482.215 790.269 478.92C796.178 475.633 803.73 477.588 807.173 483.251Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M792.227 462.099C790.067 462.403 787.968 462.942 786.01 463.709L782.26 458.459C782.058 458.156 781.628 458.071 781.321 458.24L776.061 461.147C775.753 461.316 775.613 461.695 775.78 462.024L778.651 467.762C776.974 469.043 775.437 470.51 774.103 472.119L768.14 469.372C767.798 469.203 767.402 469.338 767.227 469.642L764.189 474.673C764.022 474.976 764.101 475.381 764.39 475.574L769.844 479.164C769.071 481.044 768.509 483.058 768.193 485.097L761.572 485.594C761.203 485.619 760.922 485.914 760.922 486.268V492.066C760.922 492.42 761.203 492.723 761.572 492.749L768.193 493.263C768.509 495.336 769.071 497.325 769.844 499.195L764.355 502.794C764.048 502.979 763.96 503.392 764.136 503.687L767.174 508.726C767.341 509.021 767.736 509.165 768.079 508.996L774.076 506.249C775.411 507.85 776.948 509.325 778.616 510.606L775.753 516.319C775.578 516.648 775.718 517.027 776.034 517.187L781.294 520.103C781.602 520.288 782.032 520.187 782.225 519.909L785.975 514.684C787.942 515.418 790.041 515.965 792.174 516.26L792.684 522.606C792.71 522.96 793.026 523.23 793.395 523.23H799.445C799.814 523.23 800.121 522.96 800.157 522.606L800.692 516.26C802.852 515.965 804.898 515.418 806.857 514.684L810.606 519.909C810.808 520.213 811.23 520.288 811.546 520.103L816.797 517.187C817.113 517.027 817.254 516.614 817.087 516.319L814.215 510.572C815.893 509.3 817.429 507.825 818.764 506.224L824.753 508.971C825.095 509.131 825.49 508.996 825.666 508.701L828.704 503.662C828.906 503.367 828.792 502.954 828.502 502.769L823.023 499.17C823.787 497.291 824.358 495.311 824.674 493.238L831.286 492.723C831.655 492.698 831.945 492.395 831.945 492.041V486.243C831.945 485.889 831.655 485.594 831.286 485.56L824.674 485.072C824.358 483.007 823.787 481.018 823.023 479.139L828.476 475.549C828.792 475.355 828.871 474.951 828.678 474.647L825.64 469.616C825.438 469.313 825.043 469.178 824.726 469.338L818.764 472.094C817.429 470.484 815.893 469.018 814.215 467.737L817.087 461.99C817.254 461.67 817.113 461.282 816.797 461.122L811.546 458.215C811.23 458.021 810.808 458.13 810.606 458.425L806.857 463.684C804.898 462.942 802.826 462.403 800.666 462.099L800.121 455.762C800.095 455.408 799.788 455.139 799.419 455.139H793.36C792.991 455.139 792.684 455.408 792.657 455.762L792.227 462.099ZM796.46 477.074C803.423 477.074 809.078 482.485 809.078 489.159C809.078 495.825 803.423 501.243 796.46 501.243C789.505 501.243 783.85 495.825 783.85 489.159C783.85 482.485 789.47 477.074 796.46 477.074Z" fill="#631AEB"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M813.504 480.15C810.896 475.793 806.602 472.658 801.544 471.352C796.486 470.045 791.147 470.728 786.607 473.231C782.058 475.734 778.792 479.846 777.431 484.692C776.061 489.537 776.772 494.653 779.389 499.009C781.997 503.366 786.291 506.493 791.349 507.799C796.407 509.105 801.746 508.422 806.286 505.92C810.835 503.417 814.101 499.304 815.462 494.459C816.797 489.622 816.121 484.498 813.504 480.15ZM800.297 475.818C804.099 476.796 807.34 479.164 809.298 482.459C811.291 485.754 811.801 489.588 810.782 493.262C809.754 496.911 807.287 500.012 803.845 501.891C800.411 503.796 796.407 504.285 792.57 503.307C788.759 502.33 785.527 499.962 783.56 496.667C781.575 493.372 781.066 489.537 782.085 485.863C783.112 482.214 785.58 479.113 789.022 477.234C792.455 475.355 796.486 474.84 800.297 475.818Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M796.459 470.594C791.12 470.594 786.264 472.658 782.769 476.038C779.274 479.383 777.088 484.035 777.088 489.159C777.088 494.274 779.248 498.926 782.769 502.271C786.264 505.625 791.12 507.715 796.459 507.715C801.798 507.715 806.663 505.65 810.149 502.271C813.644 498.926 815.831 494.274 815.831 489.159C815.831 484.035 813.679 479.383 810.149 476.038C806.663 472.692 801.798 470.594 796.459 470.594ZM785.746 478.894C788.477 476.282 792.288 474.647 796.459 474.647C800.639 474.647 804.441 476.282 807.172 478.894C809.93 481.532 811.598 485.156 811.598 489.159C811.598 493.153 809.895 496.802 807.172 499.414C804.415 502.027 800.639 503.662 796.459 503.662C792.288 503.662 788.477 502.027 785.746 499.414C782.998 496.777 781.32 493.153 781.32 489.159C781.294 485.156 782.998 481.532 785.746 478.894Z" fill="#9464F1"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M784.271 489.158C784.271 482.703 789.724 477.503 796.433 477.503C803.168 477.503 808.595 482.728 808.595 489.158C808.595 495.605 803.133 500.804 796.433 500.804C789.724 500.83 784.271 495.605 784.271 489.158ZM807.365 483.116C803.87 477.343 796.178 475.355 790.128 478.675C784.104 482.02 782.031 489.403 785.491 495.2C788.986 500.964 796.687 502.953 802.737 499.633C808.788 496.287 810.86 488.914 807.365 483.116Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M218.117 259.748H280.701V285.434H218.117V259.748Z" fill="#9464F1"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M296.842 317.987H406.379V326.044H296.842V317.987Z" fill="#9464F1"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M296.842 372.587H375.135V380.643H296.842V372.587Z" fill="#9464F1"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M296.842 427.186H385.532V435.242H296.842V427.186Z" fill="#9464F1"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M296.842 335.078H544.192V339.106H296.842V335.078Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M296.842 389.677H544.192V393.705H296.842V389.677Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M296.842 444.275H492.804V448.304H296.842V444.275Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M296.842 343.951H397.8V347.979H296.842V343.951Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M296.842 398.551H444.34V402.579H296.842V398.551Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M296.842 453.149H454.395V457.178H296.842V453.149Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M296.842 291.939H322.887V295.968H296.842V291.939Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M333.854 291.939H369.647V295.968H333.854V291.939Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M381.896 291.939H415.704V295.968H381.896V291.939Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M426.47 291.939H460.269V295.968H426.47V291.939Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M471.781 291.939H507.995V295.968H471.781V291.939Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M518.963 291.939H551.691V295.968H518.963V291.939Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M563.932 291.939H589.986V295.968H563.932V291.939Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M296.842 259.748H589.96V285.434H296.842V259.748Z" fill="#9464F1"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M301.442 263.667H557.627V281.465H301.442V263.667Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M567.83 267.037C568.392 266.498 569.025 266.11 569.701 265.841C570.412 265.571 571.176 265.402 571.923 265.402C572.687 265.402 573.424 265.537 574.135 265.841C574.82 266.11 575.47 266.523 576.006 267.037C576.577 267.585 576.972 268.183 577.262 268.832C577.543 269.515 577.718 270.248 577.718 270.956C577.718 271.554 577.604 272.152 577.402 272.725C577.262 273.164 577.033 273.593 576.779 273.981L577.174 274.335H577.288C577.455 274.335 577.657 274.386 577.771 274.52L580.642 277.268C580.783 277.411 580.897 277.571 580.985 277.731C581.064 277.925 581.099 278.119 581.099 278.279V278.717H581.011L580.95 278.827C580.871 278.987 580.756 279.147 580.616 279.282L580.581 279.315C580.44 279.45 580.274 279.56 580.098 279.61C579.931 279.695 579.729 279.72 579.536 279.72C579.334 279.72 579.132 279.695 578.965 279.61C578.789 279.535 578.623 279.425 578.482 279.282L575.611 276.535C575.47 276.4 575.409 276.24 575.409 276.046V275.911L575.04 275.557C574.618 275.801 574.188 276.02 573.74 276.155C573.143 276.349 572.511 276.459 571.887 276.459C571.123 276.459 570.386 276.315 569.675 276.02C568.99 275.751 568.34 275.338 567.795 274.824C567.233 274.301 566.829 273.678 566.548 273.029C566.267 272.346 566.092 271.613 566.092 270.905C566.092 270.172 566.241 269.464 566.548 268.782C566.865 268.183 567.26 267.551 567.83 267.037ZM571.923 266.742C571.352 266.742 570.781 266.843 570.245 267.062C569.727 267.282 569.244 267.585 568.823 267.989C568.392 268.402 568.085 268.857 567.857 269.355C567.629 269.869 567.514 270.416 567.514 270.956C567.514 271.504 567.629 272.043 567.857 272.565C568.085 273.054 568.392 273.518 568.823 273.922C569.244 274.335 569.727 274.63 570.245 274.849C570.781 275.068 571.352 275.178 571.923 275.178C572.485 275.178 573.055 275.068 573.591 274.849C574.109 274.63 574.592 274.335 575.014 273.922L575.216 273.737L575.382 273.543C575.672 273.189 575.892 272.784 576.067 272.372C576.234 271.908 576.322 271.445 576.322 270.956C576.322 270.416 576.208 269.869 575.979 269.355C575.786 268.857 575.444 268.402 575.014 267.989C574.592 267.585 574.109 267.282 573.591 267.062C573.082 266.843 572.511 266.742 571.923 266.742Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M218.346 458.509L300.617 353.129C302.066 351.275 304.683 350.702 306.808 351.789C310.303 353.533 314.281 354.511 318.514 354.485C322.948 354.435 327.119 353.289 330.702 351.275C332.774 350.129 335.33 350.541 336.893 352.286L374.652 394.716C375.785 395.989 376.127 397.649 375.557 399.258C374.705 401.677 374.248 404.289 374.283 407.011C374.424 420.082 385.585 430.557 399.196 430.422C412.833 430.287 423.766 419.593 423.625 406.556C423.599 403.067 422.773 399.747 421.298 396.781C420.525 395.23 420.675 393.545 421.693 392.154L492.4 293.195C493.568 291.56 495.552 290.827 497.572 291.291C499.478 291.72 501.497 291.939 503.543 291.939C508.628 291.889 513.369 290.364 517.26 287.802C518.91 286.715 520.983 286.689 522.686 287.726L564.415 313.445C566.092 314.474 566.918 316.243 566.61 318.122C566.408 319.403 566.294 320.684 566.32 322.016C566.461 335.078 577.631 345.561 591.233 345.426C604.87 345.283 615.812 334.589 615.671 321.552C615.636 318.721 615.1 315.999 614.108 313.496C613.423 311.76 613.818 309.906 615.188 308.575L707.259 218.59C708.735 217.149 710.921 216.77 712.853 217.638C716.12 219.138 719.817 219.955 723.68 219.896C737.318 219.762 748.25 209.068 748.11 196.031C747.969 182.969 736.8 172.486 723.197 172.62C709.56 172.755 698.619 183.458 698.759 196.494C698.794 200.194 699.725 203.708 701.35 206.809C702.289 208.629 701.947 210.728 700.472 212.194L609.814 300.704C608.198 302.288 605.757 302.583 603.79 301.437C600.014 299.237 595.553 298.015 590.811 298.041C584.392 298.091 578.597 300.485 574.25 304.378C572.599 305.853 570.158 306.063 568.252 304.9L528.939 280.672C527.059 279.501 526.234 277.378 526.919 275.313C527.683 272.995 528.087 270.552 528.052 267.99C527.911 254.928 516.75 244.444 503.139 244.588C489.502 244.723 478.569 255.417 478.71 268.453C478.771 273.948 480.756 278.962 484.075 282.956C485.445 284.591 485.524 286.799 484.277 288.543L416.1 383.989C414.703 385.918 412.095 386.576 409.909 385.539C406.554 383.963 402.778 383.095 398.765 383.121C394.111 383.171 389.765 384.452 386.068 386.626C383.995 387.857 381.352 387.444 379.763 385.674L342.346 343.682C341.16 342.35 340.844 340.656 341.468 339.03C342.46 336.418 343.004 333.586 342.978 330.645C342.829 317.583 331.668 307.1 318.057 307.235C304.42 307.378 293.487 318.072 293.627 331.109C293.654 334.479 294.427 337.69 295.814 340.606C296.587 342.215 296.385 343.951 295.278 345.342L210.934 452.981C209.652 454.616 207.492 455.273 205.472 454.616C202.952 453.832 200.274 453.394 197.464 453.419C183.827 453.554 172.894 464.248 173.035 477.293C173.175 490.355 184.345 500.83 197.947 500.695C211.584 500.56 222.526 489.866 222.376 476.83C222.324 472.06 220.813 467.627 218.232 463.928C217.011 462.242 217.064 460.144 218.346 458.509Z" fill="#F51D2D"/>
<path d="M349.169 621.211C349.169 621.388 349.055 621.565 348.836 621.734C348.616 621.911 348.291 622.079 347.852 622.256C347.413 622.425 346.869 622.593 346.219 622.762C345.569 622.93 344.814 623.091 343.962 623.251C343.101 623.419 342.144 623.571 341.09 623.731C340.028 623.883 338.878 624.034 337.631 624.178C336.393 624.321 335.058 624.464 333.635 624.599C332.213 624.734 330.711 624.86 329.122 624.987C327.541 625.105 325.881 625.223 324.143 625.332C322.413 625.45 320.613 625.551 318.751 625.644C316.889 625.745 314.966 625.838 312.991 625.914C311.015 625.998 308.995 626.074 306.923 626.141C304.85 626.209 302.743 626.268 300.6 626.318C298.449 626.369 296.28 626.411 294.084 626.445C291.88 626.478 289.667 626.504 287.437 626.52C285.207 626.537 282.976 626.546 280.728 626.546C278.489 626.546 276.25 626.537 274.019 626.52C271.789 626.504 269.576 626.478 267.381 626.445C265.185 626.411 263.008 626.369 260.865 626.318C258.722 626.268 256.615 626.209 254.542 626.141C252.47 626.074 250.45 625.998 248.475 625.914C246.49 625.838 244.576 625.745 242.714 625.644C240.844 625.551 239.052 625.45 237.314 625.332C235.584 625.223 233.924 625.105 232.343 624.987C230.754 624.86 229.252 624.734 227.83 624.599C226.407 624.464 225.073 624.321 223.826 624.178C222.588 624.034 221.428 623.883 220.375 623.731C219.321 623.571 218.364 623.419 217.503 623.251C216.643 623.091 215.896 622.93 215.238 622.762C214.588 622.593 214.043 622.425 213.613 622.256C213.174 622.079 212.84 621.911 212.621 621.734C212.401 621.565 212.296 621.388 212.296 621.211C212.296 621.034 212.401 620.866 212.621 620.689C212.84 620.512 213.174 620.343 213.613 620.175C214.043 619.998 214.588 619.829 215.238 619.661C215.896 619.501 216.643 619.332 217.503 619.172C218.364 619.012 219.321 618.852 220.375 618.7C221.428 618.54 222.588 618.397 223.826 618.245C225.073 618.102 226.407 617.967 227.83 617.832C229.252 617.697 230.754 617.562 232.343 617.444C233.924 617.318 235.584 617.2 237.314 617.09C239.052 616.981 240.844 616.871 242.714 616.779C244.576 616.678 246.49 616.593 248.475 616.509C250.45 616.425 252.47 616.349 254.542 616.281C256.615 616.214 258.722 616.155 260.865 616.104C263.008 616.054 265.185 616.012 267.381 615.978C269.576 615.944 271.789 615.919 274.019 615.902C276.25 615.885 278.489 615.877 280.728 615.877C282.976 615.877 285.207 615.885 287.437 615.902C289.667 615.919 291.88 615.944 294.084 615.978C296.28 616.012 298.449 616.054 300.6 616.104C302.743 616.155 304.85 616.214 306.923 616.281C308.995 616.349 311.015 616.425 312.991 616.509C314.966 616.593 316.889 616.678 318.751 616.779C320.613 616.871 322.413 616.981 324.143 617.09C325.881 617.2 327.541 617.318 329.122 617.444C330.711 617.562 332.213 617.697 333.635 617.832C335.058 617.967 336.393 618.102 337.631 618.245C338.878 618.397 340.028 618.54 341.09 618.7C342.144 618.852 343.101 619.012 343.962 619.172C344.814 619.332 345.569 619.501 346.219 619.661C346.869 619.829 347.413 619.998 347.852 620.175C348.291 620.343 348.616 620.512 348.836 620.689C349.055 620.866 349.169 621.034 349.169 621.211Z" fill="#B6D8F8"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M261.101 600.607L260.364 608.419L249.598 609.211L248.939 599.823L261.101 600.607Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M249.256 605.512C247.719 605.781 247.096 616.096 250.617 617.975C258.002 621.894 257.747 622.897 261.075 627.717C264.509 632.967 275.362 632.807 277.233 626.681C277.233 626.681 277.382 624.776 274.396 622.357C274.396 622.357 269.453 618.085 267.038 612.178C267.038 612.178 266.897 607.467 263.543 606.295C263.543 606.295 260.276 605.074 260.505 606.624C260.873 608.883 250.134 608.967 249.256 605.512Z" fill="#1A1840"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M291.977 599.031L291.275 606.464L281.044 607.197L280.395 598.298L291.977 599.031Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M280.789 603.927C279.393 604.256 278.883 615.059 282.036 616.888C287.234 619.905 286.927 614.326 297.579 622.028C297.579 622.028 302.294 625.433 308.23 623.823C308.23 623.823 312.805 623.25 313.71 619.222C313.71 619.222 313.798 617.099 310.812 615.438C310.812 615.438 303.541 613.888 298.887 608.368C298.887 608.368 297.438 604.34 294.312 603.497C294.312 603.497 289.561 602.756 289.456 604.205C289.14 608.444 280.649 605.402 280.789 603.927Z" fill="#1A1840"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M293.46 601.129C293.46 601.129 277.803 603.143 278.119 600.227C279.761 584.983 276.899 567.488 278.998 558.555C280.394 552.656 274.879 546.176 274.168 539.62L264.14 487.818L295.076 488.88C296.78 500.535 301.495 531.15 298.659 557.199C297.438 568.381 294.084 587.325 293.46 601.129Z" fill="#5416CE"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M283.854 601.804C280.675 601.804 277.979 601.45 278.12 600.228C279.762 584.983 276.899 567.488 278.998 558.556C280.394 552.657 274.879 546.176 274.168 539.62L267.661 506.089L281.694 504.176C281.694 504.176 280.877 535.803 284.96 552.269C284.995 552.269 285.873 581.309 283.854 601.804Z" fill="#360E93"/>
<mask id="mask3_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="245" y="487" width="52" height="118">
<path d="M245.032 487.558H296.736V604.054H245.032V487.558Z" fill="white"/>
</mask>
<g mask="url(#mask3_2476_449)">
<mask id="mask4_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="246" y="488" width="50" height="115">
<path d="M249.713 488.013C247.21 494.569 245.99 506.03 249.625 518.847C249.23 528.892 252.611 544.272 252.549 554.014C252.523 561.252 247.008 584.14 246.332 601.753C251.074 603.278 257.66 603.304 263.034 602.023C265.502 583.433 268.996 573.008 272.21 556.626C274.054 547.238 275.503 534.387 277.295 525.547C278.91 517.491 280.535 514.684 286.216 508.288C290.528 503.417 295.446 494.569 295.077 488.83L249.713 488.013Z" fill="white"/>
</mask>
<g mask="url(#mask4_2476_449)">
<path d="M245.989 488.013V603.304H295.445V488.013H245.989Z" fill="url(#paint1_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M249.713 488.013C247.21 494.569 245.99 506.03 249.625 518.847C249.23 528.892 252.611 544.272 252.549 554.014C252.523 561.252 247.008 584.14 246.332 601.753C247.974 602.301 249.88 602.621 251.864 602.815C253.796 588.742 261.304 553.525 261.304 553.525C261.585 549.084 258.854 515.611 258.854 515.611C256.211 507.53 257.581 495.985 259.056 488.148L249.713 488.013Z" fill="#360E93"/>
<mask id="mask5_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="287" y="412" width="21" height="44">
<path d="M287.744 412.051H307.976V455.197H287.744V412.051Z" fill="white"/>
</mask>
<g mask="url(#mask5_2476_449)">
<mask id="mask6_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="287" y="413" width="21" height="41">
<path d="M287.832 413.71C287.832 413.71 296.271 412.185 303.147 428.812C308.942 442.776 306.984 453.09 306.984 453.09L291.213 452.905L287.832 413.71Z" fill="white"/>
</mask>
<g mask="url(#mask6_2476_449)">
<path d="M287.832 412.186V453.091H308.942V412.186H287.832Z" fill="url(#paint2_linear_2476_449)"/>
</g>
</g>
<mask id="mask7_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="242" y="407" width="57" height="88">
<path d="M242.784 407.736H298.984V494.03H242.784V407.736Z" fill="white"/>
</mask>
<g mask="url(#mask7_2476_449)">
<mask id="mask8_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="244" y="408" width="54" height="85">
<path d="M283.318 410.66C292.635 412.759 302.83 430.093 294.821 467.821C294.821 467.821 290.527 485.889 285.873 490.49L269.365 492.723C269.365 492.723 249.624 492.698 246.384 488.173C245.304 486.656 243.688 483.824 245.927 478.347C252.944 461.256 247.974 426.124 248.544 415.919C250.731 412.32 261.786 407.997 263.938 408.84L283.318 410.66Z" fill="white"/>
</mask>
<g mask="url(#mask8_2476_449)">
<path d="M243.688 407.997V492.723H302.83V407.997H243.688Z" fill="url(#paint3_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M256.298 462.487C256.298 462.487 269.137 472.659 274.397 480.858L280.649 487.305L262.639 472.659L247.438 458.509L242.749 439.515L251.214 425.467L255.28 414.419C271.297 427.152 254.138 447.596 256.298 462.487Z" fill="#FF7200"/>
<mask id="mask9_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="238" y="448" width="43" height="44">
<path d="M238.288 448.726H281V491.872H238.288V448.726Z" fill="white"/>
</mask>
<g mask="url(#mask9_2476_449)">
<mask id="mask10_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="240" y="449" width="41" height="43">
<path d="M273.632 491.577L280.13 485.594C280.701 485.072 278.831 484.743 278.199 484.288C268.996 477.614 261.98 466.835 253.743 463.22C254.507 459.656 255.613 454.995 255.051 450.184L240.105 449.501C239.763 454.431 240.676 459.546 240.843 465.125C248.597 477.125 266.414 480.555 273.632 491.577Z" fill="white"/>
</mask>
<g mask="url(#mask10_2476_449)">
<path d="M239.763 449.501V491.577H280.701V449.501H239.763Z" fill="url(#paint4_linear_2476_449)"/>
</g>
</g>
<mask id="mask11_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="236" y="412" width="28" height="42">
<path d="M236.04 412.051H263.016V453.04H236.04V412.051Z" fill="white"/>
</mask>
<g mask="url(#mask11_2476_449)">
<mask id="mask12_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="237" y="413" width="25" height="40">
<path d="M255.473 452.577C256.983 444.681 260.874 436.026 261.014 431.324C260.619 413.602 253.998 411.697 247.86 415.397C244.453 417.436 241.581 422.366 241.581 430.557C241.581 436.329 238.174 443.054 237.296 449.771C243.97 450.074 251.75 450.428 255.473 452.577Z" fill="white"/>
</mask>
<g mask="url(#mask12_2476_449)">
<path d="M237.296 411.697V452.577H261.014V411.697H237.296Z" fill="url(#paint5_linear_2476_449)"/>
</g>
</g>
<mask id="mask13_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="260" y="483" width="64" height="9">
<path d="M260.768 483.243H323.712V491.872H260.768V483.243Z" fill="white"/>
</mask>
<g mask="url(#mask13_2476_449)">
<mask id="mask14_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="261" y="483" width="63" height="8">
<path d="M261.33 485.535L269.681 490.001L323.405 487.33L315.221 483.302L261.33 485.535Z" fill="white"/>
</mask>
<g mask="url(#mask14_2476_449)">
<path d="M261.33 483.302V490.001H323.405V483.302H261.33Z" fill="url(#paint6_linear_2476_449)"/>
</g>
</g>
<mask id="mask15_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="260" y="485" width="13" height="10">
<path d="M260.768 485.4H272.008V494.03H260.768V485.4Z" fill="white"/>
</mask>
<g mask="url(#mask15_2476_449)">
<mask id="mask16_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="261" y="485" width="9" height="8">
<path d="M261.33 485.535L269.655 489.757L269.795 492.555L261.98 487.549C261.558 487.28 261.304 486.841 261.33 486.353V485.535Z" fill="white"/>
</mask>
<g mask="url(#mask16_2476_449)">
<path d="M261.304 485.535V492.555H269.795V485.535H261.304Z" fill="url(#paint7_linear_2476_449)"/>
</g>
</g>
<mask id="mask17_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="267" y="485" width="57" height="10">
<path d="M267.512 485.4H323.712V494.03H267.512V485.4Z" fill="white"/>
</mask>
<g mask="url(#mask17_2476_449)">
<mask id="mask18_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="269" y="487" width="55" height="6">
<path d="M323.404 487.061L269.681 489.757L269.821 492.555L322.061 489.943C322.886 489.892 323.545 489.209 323.483 488.417L323.404 487.061Z" fill="white"/>
</mask>
<g mask="url(#mask18_2476_449)">
<path d="M269.681 487.061V492.555H323.545V487.061H269.681Z" fill="url(#paint8_linear_2476_449)"/>
</g>
</g>
<mask id="mask19_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="267" y="450" width="19" height="42">
<path d="M267.512 450.883H285.496V491.872H267.512V450.883Z" fill="white"/>
</mask>
<g mask="url(#mask19_2476_449)">
<mask id="mask20_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="268" y="452" width="16" height="39">
<path d="M280.104 454.237L268.03 489.099L269.681 490.001L283.88 452.197L282.721 452.273C281.466 452.332 280.473 453.124 280.104 454.237Z" fill="white"/>
</mask>
<g mask="url(#mask20_2476_449)">
<path d="M268.03 452.197V490.001H283.88V452.197H268.03Z" fill="url(#paint9_linear_2476_449)"/>
</g>
</g>
<mask id="mask21_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="267" y="448" width="68" height="44">
<path d="M267.512 448.726H334.952V491.872H267.512V448.726Z" fill="white"/>
</mask>
<g mask="url(#mask21_2476_449)">
<mask id="mask22_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="269" y="450" width="66" height="41">
<path d="M332.203 450.234L283.432 452.223C282.694 452.248 282.062 452.712 281.807 453.395L269.285 490.052L323.597 487.33L333.994 452.417C334.17 451.869 334.056 451.321 333.687 450.892C333.318 450.428 332.8 450.209 332.203 450.234Z" fill="white"/>
</mask>
<g mask="url(#mask22_2476_449)">
<path d="M269.285 450.209V490.052H334.17V450.209H269.285Z" fill="url(#paint10_linear_2476_449)"/>
</g>
</g>
<mask id="mask23_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="267" y="448" width="68" height="44">
<path d="M267.512 448.726H334.952V491.872H267.512V448.726Z" fill="white"/>
</mask>
<g mask="url(#mask23_2476_449)">
<mask id="mask24_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="269" y="450" width="65" height="40">
<path d="M332.238 450.479L283.458 452.468C282.809 452.493 282.291 452.872 282.097 453.445L269.681 489.757L323.404 487.061L333.74 452.307C333.88 451.844 333.801 451.38 333.485 451.027C333.169 450.647 332.721 450.453 332.238 450.479Z" fill="white"/>
</mask>
<g mask="url(#mask24_2476_449)">
<path d="M269.681 450.453V489.757H333.88V450.453H269.681Z" fill="url(#paint11_linear_2476_449)"/>
</g>
</g>
<mask id="mask25_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="276" y="483" width="21" height="14">
<path d="M276.504 483.243H296.736V496.187H276.504V483.243Z" fill="white"/>
</mask>
<g mask="url(#mask25_2476_449)">
<mask id="mask26_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="277" y="484" width="19" height="12">
<path d="M295.19 485.426C295.19 485.426 295.357 489.125 293.54 491.038C293.54 491.038 289.193 492.909 287.322 494.493C285.505 496.019 279.226 493.92 279.226 493.946C279.226 493.946 277.04 492.943 278.348 491.578C278.945 490.954 280.192 490.431 280.956 490.684C287.322 492.833 286.04 486.841 287.349 485.695C288.912 484.288 293.654 483.117 295.19 485.426Z" fill="white"/>
</mask>
<g mask="url(#mask26_2476_449)">
<path d="M277.039 483.117V496.019H295.357V483.117H277.039Z" fill="url(#paint12_linear_2476_449)"/>
</g>
</g>
<mask id="mask27_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="263" y="394" width="27" height="29">
<path d="M263.016 394.792H289.992V422.837H263.016V394.792Z" fill="white"/>
</mask>
<g mask="url(#mask27_2476_449)">
<mask id="mask28_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="264" y="395" width="26" height="27">
<path d="M289.28 408.376C289.28 408.376 285.873 412.211 288.402 416.053C288.402 416.053 286.813 421.548 278.997 421.059C274.817 420.73 264.166 414.039 264.57 408.84C264.57 408.84 266.352 408.781 266.809 407.668C266.809 407.668 268.513 401.82 267.037 395.424L289.28 408.376Z" fill="white"/>
</mask>
<g mask="url(#mask28_2476_449)">
<path d="M264.166 395.424V421.548H289.28V395.424H264.166Z" fill="url(#paint13_linear_2476_449)"/>
</g>
</g>
<mask id="mask29_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="260" y="373" width="39" height="42">
<path d="M260.768 373.219H298.984V414.208H260.768V373.219Z" fill="white"/>
</mask>
<g mask="url(#mask29_2476_449)">
<mask id="mask30_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="262" y="374" width="35" height="40">
<path d="M295.251 394.169C295.251 394.169 293.372 402.529 292.064 406.338C292.064 406.338 290.79 409.708 283.572 412.869C283.23 413.003 282.747 413.222 282.431 413.248C281.017 413.408 278.286 413.576 270.954 406.961C270.954 406.961 268.003 403.725 266.835 400.616C266.835 400.616 260.047 386.113 263.288 381.402C263.288 381.402 269.197 372.722 280.56 374.517C280.56 374.517 293.969 374.542 295.023 381.023C298.29 387.478 296.691 389.542 295.251 394.169Z" fill="white"/>
</mask>
<g mask="url(#mask30_2476_449)">
<path d="M260.047 372.722V413.576H298.289V372.722H260.047Z" fill="url(#paint14_linear_2476_449)"/>
</g>
</g>
<mask id="mask31_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="258" y="366" width="50" height="40">
<path d="M258.52 366.747H307.976V405.579H258.52V366.747Z" fill="white"/>
</mask>
<g mask="url(#mask31_2476_449)">
<mask id="mask32_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="259" y="368" width="48" height="37">
<path d="M306.553 384.832C306.553 384.832 301.381 384.967 298.457 382.927C298.457 382.927 300.933 382.902 303.515 379.742C294.619 382.523 287.489 360.368 268.767 371.82C268.539 370.733 268.513 369.242 269.11 368.205C267.178 370 265.589 372.014 265.246 373.893L262.348 371.77L262.919 376.101C255.244 379.801 261.637 392.264 263.859 396.891C264.254 400.7 266.388 402.824 267.951 404.593L267.722 400.371C265.018 398.248 262.322 390.335 267.916 392.913C268.46 395.939 268.32 395.778 269.4 398.745L270.076 398.635C269.848 396.866 270.216 395.281 270.506 394.034C271.13 393.764 273.571 391.666 272.719 388.076C272.719 388.076 279.085 389.922 281.579 390.714C281.579 390.714 279.533 387.335 279.454 386.77C279.085 384.781 299.396 399.908 306.553 384.832Z" fill="white"/>
</mask>
<g mask="url(#mask32_2476_449)">
<path d="M255.244 360.368V404.593H306.553V360.368H255.244Z" fill="url(#paint15_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M681.574 343.598H752.544C754.274 343.598 755.723 344.963 755.723 346.648V391.177C755.723 392.837 754.301 394.219 752.544 394.219H714.249L687.765 407.315C687.572 407.424 687.344 407.399 687.177 407.231C687.001 407.096 686.948 406.851 687.027 406.658L692.173 394.219H681.574C679.844 394.219 678.396 392.862 678.396 391.177V346.648C678.396 344.963 679.818 343.598 681.574 343.598Z" fill="#B6D8F8"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M709.56 391.936L682.311 405.411C682.004 405.571 681.635 405.52 681.381 405.301C681.117 405.082 681.038 404.728 681.179 404.433L686.351 391.936H675.804C673.873 391.936 672.283 390.41 672.283 388.565V342.595C672.283 340.741 673.873 339.216 675.804 339.216H749.014C750.946 339.216 752.544 340.741 752.544 342.595V388.531C752.544 390.385 750.946 391.91 749.014 391.91H709.56V391.936Z" fill="#217DC4"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M675.778 339.46H748.988C750.779 339.46 752.254 340.876 752.254 342.595V388.531C752.254 390.25 750.779 391.666 748.988 391.666H709.472L682.171 405.166C681.969 405.275 681.714 405.25 681.548 405.082C681.381 404.922 681.319 404.702 681.407 404.483L686.72 391.666H675.778C673.987 391.666 672.512 390.25 672.512 388.531V342.595C672.512 340.876 673.987 339.46 675.778 339.46Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M736.659 366.569H742.885V382.657H736.659V366.569Z" fill="#631AEB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M727.544 373.918H733.761V382.657H727.544V373.918Z" fill="#631AEB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M718.42 359.222H724.646V382.657H718.42V359.222Z" fill="#631AEB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M709.306 349.176H715.523V382.657H709.306V349.176Z" fill="#631AEB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M700.182 362.406H706.408V382.631H700.182V362.406Z" fill="#631AEB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M691.066 356.609H697.284V382.632H691.066V356.609Z" fill="#631AEB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M681.943 367.632H688.169V382.657H681.943V367.632Z" fill="#631AEB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M226.443 333.063H155.165C153.4 333.063 151.986 334.429 151.986 336.114V380.828C151.986 382.522 153.4 383.879 155.165 383.879H193.627L220.217 397.025C220.419 397.135 220.673 397.109 220.849 396.941C221.016 396.806 221.077 396.562 220.989 396.376L215.817 383.879H226.443C228.208 383.879 229.621 382.522 229.621 380.828V336.114C229.621 334.429 228.208 333.063 226.443 333.063Z" fill="#B6D8F8"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M198.316 381.621L225.678 395.146C225.986 395.315 226.355 395.256 226.609 395.036C226.873 394.826 226.952 394.472 226.811 394.169L221.613 381.621H232.212C234.17 381.621 235.759 380.095 235.759 378.216V332.086C235.759 330.207 234.17 328.682 232.212 328.682H158.713C156.754 328.682 155.165 330.207 155.165 332.086V378.216C155.165 380.095 156.754 381.621 158.713 381.621H198.316Z" fill="#217DC4"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M232.237 328.926H158.712C156.894 328.926 155.419 330.342 155.419 332.086V378.216C155.419 379.96 156.894 381.376 158.712 381.376H198.403L225.818 394.927C226.011 395.036 226.275 395.011 226.442 394.851C226.609 394.682 226.67 394.472 226.582 394.253L221.243 381.376H232.237C234.055 381.376 235.53 379.96 235.53 378.216V332.086C235.53 330.342 234.055 328.926 232.237 328.926Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M178.549 353.727C179.084 353.07 179.708 352.497 180.366 351.983L179.198 349.64C179.111 349.455 179.172 349.235 179.365 349.151L181.587 347.929C181.754 347.82 182.008 347.87 182.123 348.039L183.659 350.188C184.45 349.884 185.31 349.665 186.127 349.564L186.329 346.977C186.355 346.783 186.531 346.623 186.724 346.623H189.279C189.481 346.623 189.683 346.783 189.683 346.977L189.912 349.564C190.763 349.699 191.58 349.918 192.379 350.213L193.916 348.064C194.03 347.904 194.258 347.845 194.452 347.955L196.673 349.176C196.84 349.286 196.928 349.505 196.84 349.665L195.672 352.008C196.357 352.53 196.954 353.095 197.49 353.752L199.94 352.64C200.133 352.556 200.361 352.606 200.449 352.8L201.722 354.924C201.837 355.084 201.784 355.328 201.608 355.438L199.369 356.913C199.676 357.671 199.905 358.463 200.019 359.281L202.723 359.466C202.917 359.491 203.092 359.66 203.092 359.845V362.297C203.092 362.491 202.917 362.677 202.723 362.677L200.019 362.896C199.878 363.713 199.65 364.505 199.343 365.264L201.582 366.73C201.758 366.84 201.81 367.059 201.696 367.253L200.423 369.376C200.309 369.536 200.08 369.621 199.905 369.536L197.463 368.424C196.928 369.048 196.331 369.646 195.646 370.16L196.814 372.503C196.893 372.696 196.84 372.916 196.638 372.991L194.425 374.222C194.258 374.331 193.995 374.272 193.89 374.112L192.353 371.963C191.554 372.258 190.728 372.477 189.876 372.587L189.648 375.199C189.622 375.393 189.455 375.553 189.253 375.553H186.698C186.496 375.553 186.303 375.393 186.303 375.199L186.101 372.612C185.249 372.477 184.423 372.258 183.633 371.989L182.096 374.137C181.982 374.298 181.754 374.357 181.552 374.247L179.339 373.025C179.172 372.916 179.084 372.696 179.172 372.528L180.331 370.194C179.655 369.671 179.058 369.098 178.514 368.449L176.072 369.562C175.87 369.646 175.651 369.595 175.563 369.401L174.281 367.278C174.167 367.118 174.228 366.873 174.395 366.764L176.643 365.289C176.327 364.531 176.099 363.738 175.985 362.921L173.262 362.702C173.06 362.677 172.894 362.517 172.894 362.323V359.879C172.894 359.685 173.06 359.491 173.262 359.491L175.958 359.306C176.099 358.488 176.327 357.696 176.617 356.938L174.369 355.463C174.202 355.354 174.14 355.143 174.255 354.949L175.537 352.825C175.651 352.665 175.87 352.581 176.046 352.665L178.549 353.727ZM183.484 358.539C182.035 360.941 182.895 363.983 185.389 365.373C187.892 366.764 191.071 365.946 192.52 363.553C193.969 361.151 193.117 358.109 190.614 356.719C188.12 355.328 184.906 356.171 183.484 358.539Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M189.798 349.615C190.702 349.75 191.58 349.969 192.441 350.297L194.03 348.089C194.109 347.955 194.285 347.929 194.425 348.005L196.638 349.236C196.779 349.311 196.84 349.48 196.752 349.615L195.558 352.033C196.269 352.581 196.928 353.205 197.464 353.862L199.993 352.691C200.133 352.64 200.309 352.665 200.388 352.8L201.67 354.924C201.758 355.059 201.696 355.219 201.582 355.303L199.281 356.828C199.598 357.62 199.852 358.463 199.993 359.331L202.776 359.525C202.917 359.525 203.057 359.66 203.057 359.795V362.247C203.057 362.382 202.952 362.517 202.776 362.517L199.993 362.736C199.852 363.604 199.624 364.446 199.281 365.239L201.582 366.764C201.723 366.84 201.758 367.008 201.67 367.143L200.388 369.267C200.309 369.402 200.133 369.452 199.993 369.376L197.464 368.23C196.893 368.913 196.243 369.536 195.558 370.059L196.779 372.477C196.84 372.612 196.814 372.772 196.673 372.857L194.452 374.087C194.311 374.163 194.144 374.112 194.057 374.003L192.467 371.795C191.642 372.124 190.764 372.343 189.85 372.477L189.648 375.14C189.648 375.284 189.508 375.419 189.367 375.419H186.812C186.645 375.419 186.531 375.309 186.531 375.14L186.303 372.477C185.389 372.343 184.511 372.124 183.686 371.795L182.096 374.003C182.009 374.138 181.842 374.163 181.701 374.087L179.48 372.857C179.339 372.772 179.286 372.612 179.366 372.477L180.586 370.059C179.884 369.511 179.225 368.913 178.689 368.23L176.16 369.376C176.02 369.427 175.844 369.402 175.756 369.267L174.483 367.143C174.395 367.008 174.457 366.84 174.571 366.764L176.872 365.239C176.529 364.446 176.301 363.604 176.16 362.736L173.377 362.517C173.236 362.517 173.087 362.382 173.087 362.247V359.795C173.087 359.635 173.201 359.525 173.377 359.525L176.16 359.331C176.301 358.463 176.529 357.62 176.872 356.828L174.571 355.303C174.422 355.219 174.395 355.059 174.483 354.924L175.756 352.8C175.844 352.665 176.02 352.606 176.16 352.691L178.689 353.862C179.251 353.179 179.91 352.556 180.586 352.008L179.401 349.589C179.339 349.455 179.366 349.286 179.515 349.21L181.728 347.98C181.868 347.904 182.035 347.955 182.123 348.064L183.712 350.272C184.538 349.943 185.416 349.724 186.329 349.589L186.557 346.918C186.557 346.783 186.698 346.648 186.838 346.648H189.394C189.569 346.648 189.683 346.758 189.683 346.918L189.798 349.615ZM188.006 355.96C185.047 355.96 182.694 358.244 182.694 361.042C182.694 363.848 185.082 366.132 188.006 366.132C190.957 366.132 193.319 363.848 193.319 361.042C193.319 358.244 190.93 355.96 188.006 355.96Z" fill="#631AEB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M180.789 357.232C181.895 355.412 183.687 354.081 185.847 353.533C187.981 352.985 190.246 353.263 192.152 354.325C194.057 355.387 195.445 357.097 196.015 359.171C196.586 361.21 196.296 363.384 195.19 365.213C194.084 367.033 192.292 368.364 190.132 368.912C188.007 369.452 185.733 369.182 183.827 368.12C181.93 367.058 180.534 365.348 179.963 363.275C179.401 361.235 179.709 359.086 180.789 357.232ZM186.382 355.412C184.767 355.817 183.406 356.828 182.58 358.218C181.755 359.6 181.526 361.235 181.957 362.76C182.378 364.311 183.432 365.617 184.881 366.409C186.33 367.193 188.033 367.412 189.623 367.008C191.247 366.595 192.608 365.592 193.434 364.202C194.259 362.811 194.479 361.185 194.057 359.634C193.627 358.075 192.582 356.769 191.133 355.985C189.684 355.218 187.981 354.999 186.382 355.412Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M188.006 353.204C190.281 353.204 192.327 354.081 193.802 355.496C195.277 356.912 196.191 358.867 196.191 361.041C196.191 363.224 195.277 365.179 193.802 366.595C192.327 368.01 190.281 368.887 188.006 368.887C185.732 368.887 183.686 368.01 182.211 366.595C180.736 365.179 179.822 363.224 179.822 361.041C179.822 358.867 180.736 356.912 182.211 355.496C183.686 354.106 185.732 353.204 188.006 353.204ZM192.52 356.718C191.361 355.606 189.763 354.923 188.006 354.923C186.241 354.923 184.652 355.606 183.484 356.718C182.325 357.831 181.614 359.356 181.614 361.041C181.614 362.735 182.325 364.26 183.484 365.373C184.652 366.485 186.241 367.168 188.006 367.168C189.763 367.168 191.361 366.485 192.52 365.373C193.688 364.26 194.399 362.735 194.399 361.041C194.399 359.356 193.688 357.831 192.52 356.718Z" fill="#9464F1"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M193.144 361.041C193.144 358.328 190.843 356.12 188.007 356.12C185.162 356.12 182.861 358.328 182.861 361.041C182.861 363.763 185.162 365.971 188.007 365.971C190.843 365.971 193.144 363.763 193.144 361.041ZM183.379 358.513C184.854 356.061 188.095 355.252 190.65 356.634C193.205 358.05 194.057 361.151 192.608 363.603C191.133 366.055 187.893 366.873 185.337 365.482C182.782 364.067 181.93 360.94 183.379 358.513Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M207.123 349.041C207.404 348.713 207.721 348.418 208.063 348.148L207.466 346.943C207.404 346.867 207.466 346.732 207.554 346.699L208.687 346.075C208.774 346.024 208.888 346.05 208.968 346.134L209.767 347.221C210.162 347.053 210.592 346.943 211.049 346.892L211.154 345.561C211.154 345.451 211.242 345.367 211.356 345.367H212.664C212.779 345.367 212.866 345.451 212.866 345.561L212.981 346.892C213.402 346.943 213.832 347.053 214.254 347.221L215.053 346.134C215.106 346.05 215.22 346.024 215.334 346.075L216.467 346.699C216.555 346.758 216.616 346.867 216.555 346.943L215.957 348.148C216.3 348.418 216.616 348.713 216.897 349.041L218.144 348.468C218.232 348.418 218.372 348.468 218.399 348.553L219.057 349.64C219.11 349.724 219.084 349.834 218.996 349.918L217.863 350.676C218.03 351.055 218.144 351.468 218.205 351.898L219.593 352.008C219.707 352.008 219.795 352.092 219.795 352.202V353.449C219.795 353.558 219.707 353.643 219.593 353.643L218.205 353.752C218.144 354.157 218.03 354.57 217.863 354.974L219.031 355.741C219.11 355.791 219.145 355.901 219.084 356.011L218.434 357.098C218.372 357.182 218.258 357.233 218.179 357.182L216.923 356.609C216.642 356.938 216.326 357.233 215.984 357.511L216.581 358.707C216.642 358.783 216.581 358.926 216.502 358.952L215.36 359.575C215.281 359.634 215.167 359.601 215.079 359.525L214.28 358.404C213.885 358.572 213.464 358.682 213.007 358.733L212.893 360.064C212.893 360.174 212.805 360.258 212.691 360.258H211.382C211.268 360.258 211.189 360.174 211.189 360.064L211.075 358.733C210.645 358.682 210.223 358.572 209.793 358.404L209.003 359.491C208.941 359.575 208.827 359.601 208.713 359.55L207.58 358.926C207.492 358.867 207.44 358.758 207.492 358.682L208.089 357.477C207.747 357.207 207.44 356.912 207.15 356.584L205.903 357.157C205.815 357.207 205.675 357.157 205.648 357.072L204.99 355.985C204.937 355.901 204.963 355.791 205.051 355.707L206.219 354.949C206.043 354.57 205.929 354.157 205.877 353.727L204.48 353.617C204.366 353.617 204.287 353.533 204.287 353.423V352.176C204.287 352.067 204.366 351.982 204.48 351.982L205.877 351.873C205.929 351.468 206.043 351.055 206.219 350.651L205.077 349.884C204.99 349.834 204.963 349.724 205.025 349.614L205.675 348.527C205.736 348.443 205.85 348.393 205.929 348.443L207.123 349.041ZM209.679 351.494C208.941 352.716 209.371 354.3 210.645 354.999C211.927 355.707 213.569 355.303 214.315 354.081C215.053 352.85 214.623 351.275 213.349 350.567C212.041 349.859 210.416 350.272 209.679 351.494Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M212.893 346.917C213.376 346.976 213.797 347.111 214.254 347.271L215.053 346.134C215.106 346.075 215.193 346.049 215.246 346.075L216.388 346.698C216.44 346.732 216.467 346.808 216.44 346.892L215.817 348.148C216.186 348.417 216.502 348.746 216.809 349.1L218.118 348.502C218.205 348.468 218.284 348.502 218.32 348.552L218.969 349.639C218.996 349.698 218.996 349.808 218.917 349.833L217.722 350.625C217.889 351.03 218.003 351.468 218.091 351.898L219.514 352.007C219.593 352.007 219.654 352.092 219.654 352.142V353.398C219.654 353.482 219.593 353.533 219.514 353.533L218.091 353.642C218.03 354.08 217.889 354.51 217.722 354.923L218.917 355.682C218.969 355.741 218.996 355.816 218.969 355.875L218.32 356.962C218.293 357.013 218.205 357.047 218.118 357.013L216.809 356.415C216.528 356.769 216.186 357.072 215.817 357.367L216.44 358.623C216.467 358.707 216.44 358.783 216.388 358.816L215.246 359.44C215.193 359.465 215.106 359.465 215.053 359.381L214.228 358.243C213.797 358.403 213.349 358.513 212.893 358.597L212.779 359.954C212.779 360.038 212.691 360.089 212.638 360.089H211.33C211.242 360.089 211.189 360.038 211.189 359.954L211.075 358.597C210.618 358.538 210.162 358.403 209.74 358.243L208.915 359.381C208.853 359.44 208.774 359.465 208.713 359.44L207.58 358.816C207.519 358.783 207.492 358.707 207.519 358.623L208.151 357.367C207.773 357.097 207.466 356.769 207.15 356.415L205.85 357.013C205.762 357.047 205.675 357.013 205.648 356.962L204.99 355.875C204.963 355.816 204.963 355.707 205.051 355.682L206.245 354.923C206.07 354.51 205.956 354.08 205.877 353.642L204.454 353.533C204.366 353.533 204.313 353.448 204.313 353.398V352.142C204.313 352.066 204.366 352.007 204.454 352.007L205.877 351.898C205.929 351.468 206.07 351.03 206.245 350.625L205.051 349.833C204.99 349.774 204.963 349.698 204.99 349.639L205.648 348.552C205.675 348.502 205.762 348.468 205.85 348.502L207.15 349.1C207.44 348.746 207.773 348.443 208.151 348.148L207.519 346.892C207.492 346.808 207.519 346.732 207.58 346.698L208.713 346.075C208.774 346.049 208.889 346.049 208.915 346.134L209.705 347.271C210.135 347.111 210.592 347.002 211.049 346.917L211.154 345.561C211.154 345.476 211.242 345.426 211.303 345.426H212.603C212.691 345.426 212.752 345.476 212.752 345.561L212.893 346.917ZM211.98 350.162C210.478 350.162 209.257 351.325 209.257 352.774C209.257 354.215 210.478 355.387 211.98 355.387C213.49 355.387 214.71 354.215 214.71 352.774C214.71 351.358 213.49 350.162 211.98 350.162Z" fill="#631AEB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M208.291 350.836C208.853 349.884 209.766 349.209 210.873 348.931C211.979 348.662 213.121 348.797 214.113 349.344C215.105 349.884 215.817 350.76 216.098 351.822C216.387 352.884 216.247 353.971 215.676 354.923C215.105 355.875 214.201 356.558 213.086 356.828C211.979 357.097 210.847 356.962 209.854 356.415C208.853 355.875 208.151 354.999 207.861 353.946C207.58 352.884 207.72 351.763 208.291 350.836ZM211.154 349.917C210.337 350.128 209.626 350.651 209.196 351.358C208.774 352.066 208.66 352.909 208.888 353.693C209.117 354.485 209.652 355.167 210.39 355.572C211.128 355.985 212.014 356.094 212.831 355.875C213.657 355.656 214.368 355.142 214.798 354.434C215.22 353.726 215.334 352.884 215.105 352.092C214.877 351.299 214.341 350.625 213.604 350.212C212.866 349.808 211.979 349.698 211.154 349.917Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M211.98 348.771C213.148 348.771 214.201 349.235 214.939 349.943C215.703 350.676 216.16 351.679 216.16 352.774C216.16 353.887 215.677 354.898 214.939 355.606C214.166 356.339 213.121 356.769 211.98 356.769C210.821 356.769 209.767 356.314 209.029 355.606C208.256 354.864 207.809 353.861 207.809 352.774C207.809 351.654 208.256 350.651 209.029 349.943C209.767 349.21 210.821 348.771 211.98 348.771ZM214.315 350.566C213.718 349.993 212.893 349.639 211.98 349.639C211.075 349.639 210.25 349.993 209.653 350.566C209.056 351.139 208.687 351.932 208.687 352.8C208.687 353.668 209.056 354.46 209.653 355.033C210.25 355.606 211.075 355.96 211.98 355.96C212.893 355.96 213.718 355.606 214.315 355.033C214.913 354.46 215.281 353.668 215.281 352.8C215.281 351.932 214.913 351.139 214.315 350.566Z" fill="#9464F1"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M214.623 352.8C214.623 351.41 213.463 350.272 211.979 350.272C210.53 350.272 209.336 351.41 209.336 352.8C209.336 354.191 210.504 355.328 211.979 355.328C213.428 355.303 214.623 354.191 214.623 352.8ZM209.626 351.494C210.39 350.238 212.041 349.808 213.349 350.542C214.649 351.275 215.105 352.851 214.342 354.106C213.569 355.353 211.927 355.792 210.618 355.059C209.31 354.325 208.853 352.716 209.626 351.494Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M194.856 335.432C195.049 335.187 195.303 334.968 195.558 334.758L195.11 333.856C195.075 333.772 195.11 333.696 195.163 333.662L196.015 333.207C196.076 333.173 196.19 333.173 196.217 333.258L196.814 334.075C197.121 333.965 197.437 333.881 197.78 333.831L197.859 332.819C197.859 332.744 197.947 332.685 198.008 332.685H199C199.088 332.685 199.141 332.744 199.141 332.819L199.229 333.831C199.536 333.881 199.878 333.965 200.195 334.075L200.792 333.258C200.844 333.207 200.932 333.173 200.985 333.207L201.837 333.662C201.898 333.696 201.924 333.772 201.898 333.856L201.441 334.758C201.696 334.943 201.924 335.187 202.153 335.432L203.092 335.002C203.171 334.968 203.259 335.002 203.286 335.053L203.769 335.87C203.804 335.921 203.804 336.03 203.716 336.064L202.864 336.628C202.978 336.932 203.057 337.227 203.119 337.53L204.172 337.614C204.251 337.614 204.313 337.69 204.313 337.749V338.702C204.313 338.786 204.251 338.836 204.172 338.836L203.119 338.921C203.057 339.216 202.978 339.544 202.864 339.814L203.716 340.387C203.769 340.446 203.804 340.522 203.769 340.581L203.286 341.398C203.259 341.449 203.171 341.474 203.092 341.449L202.153 341.011C201.951 341.255 201.722 341.474 201.468 341.668L201.924 342.57C201.951 342.645 201.924 342.73 201.872 342.755L201.02 343.218C200.959 343.244 200.844 343.244 200.818 343.168L200.221 342.35C199.905 342.46 199.597 342.536 199.281 342.595L199.193 343.598C199.193 343.682 199.114 343.732 199.053 343.732H198.061C197.973 343.732 197.92 343.682 197.92 343.598L197.832 342.595C197.525 342.536 197.183 342.46 196.866 342.35L196.269 343.168C196.217 343.218 196.129 343.244 196.076 343.218L195.224 342.755C195.163 342.73 195.137 342.645 195.163 342.57L195.62 341.668C195.365 341.474 195.137 341.255 194.908 340.985L193.969 341.423C193.89 341.449 193.802 341.423 193.775 341.364L193.292 340.555C193.257 340.496 193.257 340.387 193.345 340.362L194.197 339.789C194.083 339.494 193.995 339.19 193.942 338.895L192.889 338.811C192.81 338.811 192.748 338.727 192.748 338.676V337.724C192.748 337.64 192.81 337.581 192.889 337.581L193.942 337.505C193.995 337.201 194.083 336.881 194.197 336.578L193.345 336.005C193.292 335.954 193.257 335.87 193.292 335.819L193.775 335.002C193.802 334.943 193.89 334.918 193.969 334.943L194.856 335.432ZM196.752 337.286C196.217 338.213 196.524 339.384 197.49 339.898C198.456 340.412 199.676 340.117 200.221 339.19C200.757 338.263 200.449 337.092 199.483 336.578C198.544 336.03 197.297 336.359 196.752 337.286Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M199.167 333.856C199.51 333.907 199.852 333.991 200.195 334.1L200.792 333.258C200.818 333.207 200.871 333.207 200.932 333.232L201.784 333.696C201.837 333.721 201.872 333.772 201.837 333.831L201.389 334.758C201.67 334.968 201.898 335.188 202.126 335.466L203.092 335.027C203.145 335.002 203.207 335.027 203.233 335.078L203.716 335.895C203.742 335.954 203.742 336.005 203.689 336.03L202.803 336.603C202.917 336.907 203.031 337.227 203.057 337.556L204.137 337.64C204.199 337.64 204.251 337.69 204.251 337.749V338.702C204.251 338.752 204.199 338.811 204.137 338.811L203.057 338.895C203.005 339.216 202.917 339.544 202.803 339.848L203.689 340.412C203.742 340.446 203.742 340.522 203.716 340.556L203.233 341.365C203.207 341.424 203.145 341.449 203.092 341.424L202.126 340.985C201.898 341.255 201.67 341.474 201.389 341.693L201.837 342.62C201.872 342.671 201.837 342.73 201.784 342.755L200.932 343.219C200.871 343.244 200.818 343.244 200.792 343.193L200.195 342.351C199.878 342.46 199.536 342.57 199.193 342.595L199.114 343.623C199.114 343.682 199.053 343.733 199 343.733H198.008C197.947 343.733 197.894 343.682 197.894 343.623L197.806 342.595C197.464 342.536 197.121 342.46 196.814 342.351L196.217 343.193C196.19 343.244 196.129 343.244 196.076 343.219L195.224 342.755C195.163 342.73 195.137 342.671 195.163 342.62L195.62 341.693C195.339 341.474 195.11 341.255 194.882 340.985L193.916 341.424C193.854 341.449 193.802 341.424 193.775 341.365L193.292 340.556C193.257 340.497 193.257 340.446 193.319 340.412L194.197 339.848C194.083 339.544 193.969 339.216 193.942 338.895L192.862 338.811C192.81 338.811 192.748 338.752 192.748 338.702V337.749C192.748 337.69 192.81 337.64 192.862 337.64L193.942 337.556C193.995 337.227 194.083 336.907 194.197 336.603L193.319 336.03C193.257 336.005 193.257 335.954 193.292 335.895L193.775 335.078C193.802 335.027 193.854 335.002 193.916 335.027L194.882 335.466C195.11 335.188 195.339 334.968 195.62 334.758L195.163 333.831C195.137 333.772 195.163 333.721 195.224 333.696L196.076 333.232C196.129 333.207 196.19 333.207 196.217 333.258L196.814 334.1C197.121 333.991 197.464 333.881 197.806 333.856L197.894 332.82C197.894 332.769 197.947 332.71 198.008 332.71H199C199.053 332.71 199.114 332.769 199.114 332.82L199.167 333.856ZM198.491 336.275C197.349 336.275 196.445 337.151 196.445 338.238C196.445 339.325 197.349 340.202 198.491 340.202C199.624 340.202 200.537 339.325 200.537 338.238C200.537 337.151 199.624 336.275 198.491 336.275Z" fill="#631AEB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M195.734 336.772C196.156 336.064 196.84 335.541 197.666 335.356C198.491 335.137 199.343 335.246 200.081 335.651C200.818 336.064 201.354 336.713 201.556 337.505C201.784 338.289 201.67 339.106 201.24 339.814C200.818 340.522 200.133 341.044 199.308 341.23C198.491 341.449 197.64 341.339 196.893 340.935C196.156 340.522 195.62 339.873 195.418 339.081C195.19 338.289 195.304 337.48 195.734 336.772ZM197.859 336.089C197.236 336.249 196.726 336.628 196.41 337.151C196.103 337.69 196.015 338.289 196.191 338.895C196.357 339.494 196.753 339.982 197.297 340.277C197.859 340.581 198.491 340.657 199.115 340.496C199.738 340.336 200.248 339.949 200.564 339.435C200.871 338.895 200.959 338.289 200.792 337.69C200.616 337.092 200.221 336.603 199.677 336.308C199.141 336.005 198.491 335.921 197.859 336.089Z" fill="#360E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M198.491 335.213C199.369 335.213 200.133 335.542 200.704 336.089C201.275 336.629 201.609 337.396 201.609 338.213C201.609 339.03 201.275 339.789 200.704 340.337C200.133 340.876 199.343 341.205 198.491 341.205C197.604 341.205 196.84 340.876 196.27 340.337C195.708 339.789 195.365 339.03 195.365 338.213C195.365 337.396 195.708 336.629 196.27 336.089C196.84 335.567 197.604 335.213 198.491 335.213ZM200.221 336.578C199.765 336.14 199.168 335.895 198.491 335.895C197.806 335.895 197.209 336.174 196.753 336.578C196.305 337.016 196.041 337.581 196.041 338.238C196.041 338.896 196.331 339.46 196.753 339.898C197.209 340.337 197.806 340.581 198.491 340.581C199.168 340.581 199.765 340.303 200.221 339.898C200.678 339.46 200.933 338.896 200.933 338.238C200.933 337.581 200.678 336.983 200.221 336.578Z" fill="#9464F1"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M200.45 338.238C200.45 337.201 199.571 336.358 198.491 336.358C197.411 336.358 196.524 337.201 196.524 338.238C196.524 339.274 197.411 340.117 198.491 340.117C199.571 340.117 200.45 339.274 200.45 338.238ZM196.726 337.26C197.297 336.333 198.544 336.004 199.51 336.552C200.476 337.091 200.818 338.288 200.248 339.215C199.677 340.142 198.43 340.471 197.464 339.923C196.498 339.384 196.156 338.187 196.726 337.26Z" fill="#360E93"/>
<mask id="mask33_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="593" y="437" width="64" height="29">
<path d="M593.472 437.938H656.416V465.984H593.472V437.938Z" fill="white"/>
</mask>
<g mask="url(#mask33_2476_449)">
<mask id="mask34_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="593" y="438" width="62" height="28">
<path d="M636.947 452.248C637.887 447.571 639.59 443.618 641.777 438.368L654.589 443.374C653.28 449.037 643.568 462.562 641.689 465.697C628.596 461.475 617.681 449.745 612.061 447.646C607.513 449.905 602.771 449.121 600.356 446.374C598.424 448.388 596.545 450.478 595.728 451.464C594.613 452.736 593.313 451.953 594.051 450.967C595.614 448.877 600.329 440.188 600.865 440.138C605.098 439.809 608.083 440.138 612.518 441.09C618.112 442.287 631.775 448.792 636.947 452.248Z" fill="white"/>
</mask>
<g mask="url(#mask34_2476_449)">
<path d="M593.313 438.368V465.697H654.589V438.368H593.313Z" fill="url(#paint16_linear_2476_449)"/>
</g>
</g>
<mask id="mask35_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="636" y="418" width="25" height="38">
<path d="M636.184 418.522H660.912V455.197H636.184V418.522Z" fill="white"/>
</mask>
<g mask="url(#mask35_2476_449)">
<mask id="mask36_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="636" y="418" width="24" height="37">
<path d="M640.039 438.453L636.263 449.231C640.214 451.818 645.272 453.579 649.953 454.481L656.345 442.346C666.804 421.388 647.345 403.775 640.039 438.453Z" fill="white"/>
</mask>
<g mask="url(#mask36_2476_449)">
<path d="M636.263 403.775V454.481H666.804V403.775H636.263Z" fill="url(#paint17_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M588.335 454.885L588.37 454.152C588.37 453.722 588.739 453.394 589.196 453.394L637.8 454.287C638.257 454.287 638.59 454.674 638.59 455.079L638.564 455.812C638.538 457.531 637.027 458.913 635.245 458.888L591.549 458.071C589.758 458.045 588.309 456.604 588.335 454.885Z" fill="#3090D7"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M574.109 408.157L574.671 407.964C575.102 407.829 575.558 408.048 575.699 408.461L591.689 453.613L591.61 453.638C589.898 454.211 588.054 453.31 587.457 451.709L572.854 410.525C572.573 409.548 573.108 408.486 574.109 408.157Z" fill="#3EA4EB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M593.533 453.445V453.504C593.533 453.529 593.568 453.554 593.595 453.554L596.835 453.613C596.861 453.613 596.888 453.579 596.888 453.554V453.504C596.888 453.369 596.773 453.259 596.633 453.259L593.762 453.2C593.647 453.225 593.533 453.335 593.533 453.445Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M597.6 453.529V453.579C597.6 453.613 597.626 453.638 597.661 453.638L600.893 453.689C600.928 453.689 600.954 453.663 600.954 453.638V453.579C600.954 453.444 600.84 453.335 600.699 453.335L597.828 453.284C597.714 453.309 597.626 453.394 597.6 453.529Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M601.691 453.614V453.664C601.691 453.689 601.718 453.723 601.744 453.723L604.984 453.774C605.011 453.774 605.046 453.748 605.046 453.723V453.664C605.046 453.529 604.932 453.42 604.791 453.42L601.92 453.369C601.806 453.369 601.691 453.479 601.691 453.614Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M605.783 453.689V453.748C605.783 453.773 605.81 453.798 605.836 453.798L609.076 453.857C609.103 453.857 609.138 453.832 609.138 453.798V453.748C609.138 453.613 609.023 453.503 608.874 453.503L606.012 453.444C605.897 453.444 605.783 453.554 605.783 453.689Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M609.875 453.773V453.832C609.875 453.857 609.901 453.882 609.928 453.882L613.168 453.933C613.194 453.933 613.221 453.908 613.221 453.882V453.832C613.221 453.688 613.115 453.579 612.966 453.579L610.103 453.528C609.989 453.528 609.875 453.638 609.875 453.773Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M613.933 453.832V453.882C613.933 453.908 613.968 453.933 613.994 453.933L617.234 453.992C617.261 453.992 617.287 453.967 617.287 453.933V453.882C617.287 453.748 617.173 453.638 617.032 453.638L614.161 453.579C614.047 453.613 613.933 453.722 613.933 453.832Z" fill="#332A7C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M575.102 407.939L575.304 407.888C575.47 407.829 575.672 407.913 575.725 408.073L591.865 453.259C591.918 453.42 591.83 453.613 591.663 453.664L591.461 453.723C591.435 453.723 591.435 453.723 591.435 453.689L575.102 407.964C575.075 407.939 575.102 407.939 575.102 407.939Z" fill="#217DC4"/>
<mask id="mask37_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="620" y="455" width="8" height="3">
<path d="M620.448 455.197H627.192V457.355H620.448V455.197Z" fill="white"/>
</mask>
<g mask="url(#mask37_2476_449)">
<mask id="mask38_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="621" y="455" width="5" height="2">
<path d="M621.353 455.812L625.409 455.871L625.383 456.9L621.353 456.824V455.812Z" fill="white"/>
</mask>
<g mask="url(#mask38_2476_449)">
<path d="M621.353 455.812V456.9H625.409V455.812H621.353Z" fill="url(#paint18_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M621.405 456.757L621.423 455.889L625.313 455.965L625.295 456.833L621.405 456.757Z" fill="#2B2468"/>
<mask id="mask39_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="591" y="453" width="5" height="5">
<path d="M591.224 453.04H595.72V457.355H591.224V453.04Z" fill="white"/>
</mask>
<g mask="url(#mask39_2476_449)">
<mask id="mask40_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="591" y="454" width="3" height="3">
<path d="M592.945 454.86C593.48 454.86 593.911 455.299 593.875 455.813C593.875 456.335 593.428 456.74 592.883 456.714C592.347 456.714 591.917 456.276 591.944 455.762C591.979 455.24 592.4 454.835 592.945 454.86Z" fill="white"/>
</mask>
<g mask="url(#mask40_2476_449)">
<path d="M591.917 454.835V456.739H593.91V454.835H591.917Z" fill="url(#paint19_linear_2476_449)"/>
</g>
</g>
<mask id="mask41_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="591" y="453" width="5" height="5">
<path d="M591.224 453.04H595.72V457.355H591.224V453.04Z" fill="white"/>
</mask>
<g mask="url(#mask41_2476_449)">
<mask id="mask42_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="592" y="454" width="2" height="3">
<path d="M592.945 454.945C593.427 454.945 593.823 455.349 593.823 455.813C593.823 456.276 593.392 456.655 592.909 456.655C592.426 456.655 592.031 456.251 592.031 455.787C592.058 455.298 592.462 454.919 592.945 454.945Z" fill="white"/>
</mask>
<g mask="url(#mask42_2476_449)">
<path d="M592.031 454.919V456.655H593.823V454.919H592.031Z" fill="url(#paint20_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M592.945 455.324C593.2 455.324 593.428 455.543 593.428 455.788C593.428 456.032 593.2 456.251 592.945 456.251C592.681 456.251 592.462 456.032 592.462 455.788C592.462 455.518 592.681 455.324 592.945 455.324Z" fill="#1A1840"/>
<mask id="mask43_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="624" y="455" width="8" height="3">
<path d="M624.944 455.197H631.688V457.355H624.944V455.197Z" fill="white"/>
</mask>
<g mask="url(#mask43_2476_449)">
<mask id="mask44_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="626" y="455" width="6" height="2">
<path d="M626.999 455.896L631.065 455.981L631.038 456.984L626.999 456.933V455.896Z" fill="white"/>
</mask>
<g mask="url(#mask44_2476_449)">
<path d="M626.999 455.896V456.984H631.065V455.896H626.999Z" fill="url(#paint21_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M627.069 456.858L627.096 455.981L630.986 456.057L630.968 456.934L627.069 456.858Z" fill="#2B2468"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M632.689 618.27C661.237 618.27 684.42 620.073 684.42 622.298C684.42 624.531 661.264 626.326 632.689 626.326C604.133 626.326 580.95 624.531 580.95 622.298C580.95 620.098 604.106 618.27 632.689 618.27Z" fill="#B6D8F8"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M647.257 597.127C647.257 597.127 648.337 605.781 646.862 607.492C646.291 608.175 646.063 612.641 645.413 614.765C645.132 615.658 644.333 615.304 644.105 615.414C643.507 615.742 643.226 610.003 643.226 610.003C643.226 610.003 642.146 607.77 634.471 616.855C634.471 616.855 632.996 621.565 624.329 620.343C624.329 620.343 619.763 620.857 617.857 617.183C617.857 617.183 617.234 615.144 619.701 612.86C619.701 612.86 626.349 609.675 629.449 603.22C629.449 603.22 631.547 599.823 636.351 597.177C636.351 597.177 639.705 595.871 640.583 597.042C641.435 598.214 647.257 597.127 647.257 597.127Z" fill="#1A1840"/>
<mask id="mask45_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="627" y="478" width="34" height="122">
<path d="M627.192 478.928H660.912V599.738H627.192V478.928Z" fill="white"/>
</mask>
<g mask="url(#mask45_2476_449)">
<mask id="mask46_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="629" y="479" width="31" height="120">
<path d="M635.184 597.455C635.184 597.455 649.734 599.275 648.882 596.612C643.28 579.193 653.712 561.496 647.943 543.26C647.855 543.016 645.326 522.412 651.552 508.726C655.161 500.805 662.458 492.748 657.655 479.931L633.568 483.824C631.241 494.788 627.632 514.987 630.161 541.634C631.495 555.429 635.526 580.036 635.184 597.455Z" fill="white"/>
</mask>
<g mask="url(#mask46_2476_449)">
<path d="M627.632 479.931V599.275H662.458V479.931H627.632Z" fill="url(#paint22_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M642.857 598.053C646.203 598.129 649.303 597.885 648.881 596.612C643.279 579.193 653.711 561.496 647.942 543.26C647.854 543.016 645.325 522.412 651.551 508.726C655.16 500.805 662.457 492.748 657.654 479.931L644.104 482.105C633.251 529.76 637.624 542.308 642.857 598.053Z" fill="#221E54"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M658.796 605.157C658.796 605.157 659.876 613.972 658.366 615.742C657.769 616.424 657.54 620.992 656.891 623.174C656.61 624.101 655.784 623.714 655.556 623.823C654.932 624.152 654.643 618.303 654.643 618.303C654.643 618.303 653.536 616.011 645.721 625.298C645.721 625.298 644.219 630.084 635.385 628.829C635.385 628.829 630.723 629.351 628.791 625.593C628.791 625.593 628.167 623.528 630.67 621.185C630.67 621.185 637.458 617.949 640.61 611.385C640.61 611.385 642.77 607.904 647.653 605.233C647.653 605.233 651.069 603.927 651.973 605.098C652.886 606.27 658.796 605.157 658.796 605.157Z" fill="#1A1840"/>
<mask id="mask47_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="636" y="485" width="37" height="122">
<path d="M636.184 485.4H672.152V606.211H636.184V485.4Z" fill="white"/>
</mask>
<g mask="url(#mask47_2476_449)">
<mask id="mask48_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="637" y="485" width="35" height="122">
<path d="M646.519 605.511C646.519 605.511 661.351 607.382 660.464 604.669C654.782 586.896 665.443 568.871 659.559 550.255C659.471 549.985 656.916 529.002 663.282 515.038C666.944 506.982 674.417 498.766 669.561 485.67L639.187 489.209C636.772 500.367 638.142 521.409 640.697 548.57C642.032 562.643 646.862 587.705 646.519 605.511Z" fill="white"/>
</mask>
<g mask="url(#mask48_2476_449)">
<path d="M636.771 485.67V607.382H674.417V485.67H636.771Z" fill="url(#paint23_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M654.274 606.11C657.716 606.219 660.895 605.941 660.464 604.669C654.783 586.896 665.443 568.871 659.56 550.255C659.472 549.985 656.917 529.002 663.283 515.038C666.945 506.982 674.418 498.766 669.562 485.67L660.552 486.732C659.955 495.85 658.48 505.052 655.354 511.988C655.354 511.988 651.78 524.047 653.027 549.631C653.027 549.631 655.81 561.387 655.046 572.545C655.046 572.494 653.115 584.225 654.274 606.11Z" fill="#2B2468"/>
<mask id="mask49_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="627" y="409" width="46" height="88">
<path d="M627.192 409.894H672.152V496.187H627.192V409.894Z" fill="white"/>
</mask>
<g mask="url(#mask49_2476_449)">
<mask id="mask50_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="629" y="411" width="43" height="84">
<path d="M656.381 411.428C658.138 410.61 659.191 411.183 663.45 415.886C667.376 420.217 673.426 427.97 668.57 449.038C660.324 484.828 670.976 471.083 671.634 486.488C671.749 489.235 669.957 491.552 668.14 493.322C668.14 493.322 649.023 498.875 633.278 488.232C631.943 485.67 632.488 480.968 634.472 477.723C639.644 469.288 637.827 463.296 636.264 452.223C635.895 449.661 625.726 443.703 630.099 435.807C633.058 430.532 637.598 426.445 641.664 421.254C641.664 421.254 642.972 419.838 643.824 419.4C647.205 417.681 652.342 413.164 656.381 411.428Z" fill="white"/>
</mask>
<g mask="url(#mask50_2476_449)">
<path d="M625.727 410.61V498.876H673.426V410.61H625.727Z" fill="url(#paint24_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M636.861 456.116C640.154 457.802 643.851 457.996 644.307 456.411C645.045 453.908 642.287 446.644 643.341 444.276C643.789 443.214 644.307 442.127 644.816 441.091C646.011 436.06 647.969 429.824 651.183 425.661C653.027 423.268 655.749 421.085 659.016 421.607C663.336 422.315 663.881 426.857 664.732 430.616C668.341 449.939 625.182 478.271 664.926 483.277C664.926 483.277 656.065 484.448 646.889 482.299C655.126 487.195 662.089 489.429 671.178 489.049C670.554 490.709 669.334 492.176 668.113 493.406C668.113 493.406 659.587 495.875 649.216 494.358C643.938 493.507 638.336 491.738 633.252 488.316L632.716 486.681C632.233 484.254 632.742 481.018 634.077 478.457C638.959 470.754 638.257 465.26 636.861 456.116Z" fill="#F10010"/>
<mask id="mask51_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="584" y="442" width="75" height="27">
<path d="M584.48 442.253H658.664V468.141H584.48V442.253Z" fill="white"/>
</mask>
<g mask="url(#mask51_2476_449)">
<mask id="mask52_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="584" y="444" width="73" height="24">
<path d="M603.193 459.731C615.584 457.152 628.167 457.009 638.169 456.629C639.477 452.525 641.269 448.767 643.824 444.25L656.803 449.584C655.047 454.565 651.034 459.874 648.961 465.368C635.385 471.925 610.868 462.158 601.973 465.992C594.649 466.835 593.086 466.186 588.423 461.096C584.331 456.604 584.305 451.354 585.332 450.612C586.781 449.803 587.317 456.579 593.797 458.045C593.85 458.045 595.527 458.643 595.694 457.75C595.843 456.958 593.367 454.7 593.481 454.236C594.131 451.893 601.411 459.132 601.437 459.107C601.832 459.242 601.859 459.731 603.193 459.731Z" fill="white"/>
</mask>
<g mask="url(#mask52_2476_449)">
<path d="M584.305 444.25V471.925H656.803V444.25H584.305Z" fill="url(#paint25_linear_2476_449)"/>
</g>
</g>
<mask id="mask53_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="638" y="418" width="28" height="40">
<path d="M638.432 418.522H665.408V457.354H638.432V418.522Z" fill="white"/>
</mask>
<g mask="url(#mask53_2476_449)">
<mask id="mask54_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="640" y="420" width="24" height="36">
<path d="M644.271 439.809L640.521 450.587C644.473 453.174 649.531 454.944 654.22 455.846L660.613 443.702C671.01 422.719 651.577 405.106 644.271 439.809Z" fill="white"/>
</mask>
<g mask="url(#mask54_2476_449)">
<path d="M640.521 405.106V455.846H671.01V405.106H640.521Z" fill="url(#paint26_linear_2476_449)"/>
</g>
</g>
<mask id="mask55_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="640" y="399" width="21" height="26">
<path d="M640.68 399.106H660.912V424.994H640.68V399.106Z" fill="white"/>
</mask>
<g mask="url(#mask55_2476_449)">
<mask id="mask56_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="641" y="400" width="20" height="23">
<path d="M641.971 409.683C641.971 409.683 648.249 416.542 643.253 421.548C643.622 426.967 658.313 414.689 660.438 410.551C657.092 410.441 654.502 403.202 654.704 400.48L652.482 400.7L641.971 409.683Z" fill="white"/>
</mask>
<g mask="url(#mask56_2476_449)">
<path d="M641.971 400.48V426.967H660.438V400.48H641.971Z" fill="url(#paint27_linear_2476_449)"/>
</g>
</g>
<mask id="mask57_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="627" y="377" width="34" height="40">
<path d="M627.192 377.533H660.912V416.365H627.192V377.533Z" fill="white"/>
</mask>
<g mask="url(#mask57_2476_449)">
<mask id="mask58_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="628" y="379" width="31" height="36">
<path d="M631.346 398.854C632.005 401.079 630.354 405.815 630.582 406.556C630.784 407.264 633.963 406.278 634.279 406.767C635.842 409.329 636.009 409.489 638.284 414.309C639.671 417.25 654.046 407.365 654.819 400.43C654.845 400.051 655.381 400.893 655.864 400.236C659.025 396.208 661.062 387.477 655.468 389.188C655.468 389.188 650.7 377.154 641.436 379.312C641.436 379.312 630.38 380.095 629.933 385.514C627.658 391.042 629.845 395.121 631.346 398.854Z" fill="white"/>
</mask>
<g mask="url(#mask58_2476_449)">
<path d="M627.658 377.154V417.25H661.062V377.154H627.658Z" fill="url(#paint28_linear_2476_449)"/>
</g>
</g>
<mask id="mask59_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="622" y="371" width="44" height="50">
<path d="M622.696 371.062H665.408V420.68H622.696V371.062Z" fill="white"/>
</mask>
<g mask="url(#mask59_2476_449)">
<mask id="mask60_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="623" y="372" width="41" height="47">
<path d="M646.582 418.498C646.582 418.498 650.955 403.885 649.33 395.037C644.843 396.697 631.636 395.121 630.187 389.407C629.845 390.385 629.73 391.64 629.792 392.812C629.818 394.573 630.81 397.624 631.408 399.174C631.776 400.7 630.415 406.034 630.644 406.691C631.46 407.096 634.051 406.413 634.244 406.801C634.586 407.315 634.903 407.829 635.21 408.351L634.586 418.093C632.119 418.228 624.62 416.728 623.057 411.562C624.277 399.065 623.338 376.8 638.538 373.345C650.981 370.514 659.613 378.57 661.519 387.09C664.785 401.677 666.831 420.351 646.582 418.498Z" fill="white"/>
</mask>
<g mask="url(#mask60_2476_449)">
<path d="M623.057 370.514V420.351H666.831V370.514H623.057Z" fill="url(#paint29_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M480.73 620.124C515.758 620.124 544.139 622.332 544.139 625.079C544.139 627.801 515.758 630.034 480.73 630.034C445.701 630.034 417.32 627.826 417.32 625.079C417.32 622.332 445.701 620.124 480.73 620.124Z" fill="#B6D8F8"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M515.556 603.329C516.091 609.944 516.522 620.588 514.335 623.335L482.828 624.018C481.836 623.638 478.314 619.332 485.497 616.72C488.799 615.902 500.417 613.13 502.603 605.183C502.542 605.208 513.597 601.374 515.556 603.329Z" fill="#1A1840"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M490.249 604.989C490.925 612.118 491.522 623.604 489.195 626.571L455.22 627.692C454.14 627.279 450.276 622.711 457.969 619.795C461.525 618.868 474.021 615.717 476.269 607.138C476.269 607.172 488.115 602.899 490.249 604.989Z" fill="#1A1840"/>
<mask id="mask61_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="490" y="487" width="32" height="122">
<path d="M490.064 487.558H521.536V608.368H490.064V487.558Z" fill="white"/>
</mask>
<g mask="url(#mask61_2476_449)">
<mask id="mask62_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="492" y="489" width="29" height="118">
<path d="M519.508 489.758C521.809 499.693 521.097 509.511 516.408 517.323C516.154 527.502 511.324 542.857 512.088 554.528C519.025 570.615 515.328 583.922 517.093 606.161C512.685 607.063 505.897 606.928 501.524 606.624C499.847 588.144 498.055 574.155 493.173 556.492C492.233 543.48 493.173 527.342 492.233 514.356C493.656 506.005 495.043 497.679 496.466 489.319C504.905 489.833 511.069 489.235 519.508 489.758Z" fill="white"/>
</mask>
<g mask="url(#mask62_2476_449)">
<path d="M492.233 489.235V607.063H521.809V489.235H492.233Z" fill="url(#paint30_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M507.89 606.842C505.581 606.842 503.342 606.733 501.524 606.623C499.847 588.143 498.055 574.154 493.173 556.491C492.233 543.479 493.173 527.341 492.233 514.355L494.279 502.271L507.917 502.161C505.642 506.383 496.211 545.577 499.899 554.257C499.899 554.283 507.917 567.597 507.89 606.842Z" fill="#1A1840"/>
<mask id="mask63_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="465" y="485" width="48" height="126">
<path d="M465.336 485.4H512.544V610.526H465.336V485.4Z" fill="white"/>
</mask>
<g mask="url(#mask63_2476_449)">
<mask id="mask64_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="466" y="487" width="45" height="122">
<path d="M510.357 488.772C510.357 488.772 512.886 507.825 499.108 518.443C495.956 529.466 491.152 543.859 486.068 553.77C493.4 569.368 489.475 587.217 492.118 606.734C487.455 609.043 480.553 609.237 475.583 608.31C474.591 589.34 465.81 562.45 466.24 551.427C470.218 533.519 472.887 508.348 478.657 487.524L510.357 488.772Z" fill="white"/>
</mask>
<g mask="url(#mask64_2476_449)">
<path d="M465.81 487.524V609.237H512.886V487.524H465.81Z" fill="url(#paint31_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M476.04 448.439L446.377 459.023C445.64 459.301 445.332 460.034 445.675 460.683L464.282 495.959C464.616 496.616 465.503 496.936 466.241 496.667L495.895 486.082C496.632 485.804 496.949 485.071 496.606 484.422L477.999 449.146C477.656 448.523 476.778 448.194 476.04 448.439Z" fill="#1A1840"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M474.987 450.723L449.135 459.976L465.758 491.442L491.61 482.215L474.987 450.723Z" fill="#F1F1FB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M466.776 450.537L462.43 452.088C461.982 451.355 460.902 451.026 459.962 451.355C459.022 451.675 458.566 452.551 458.908 453.335L454.562 454.885L456.634 458.813L468.822 454.456L466.776 450.537Z" fill="#A8A6D2"/>
<mask id="mask65_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="490" y="418" width="50" height="77">
<path d="M490.064 418.522H539.52V494.029H490.064V418.522Z" fill="white"/>
</mask>
<g mask="url(#mask65_2476_449)">
<mask id="mask66_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="492" y="420" width="46" height="74">
<path d="M508.768 485.509C507.144 488.585 505.361 490.844 502.797 491.037C501.436 493.7 498.854 494.029 497.089 493.296C494.56 492.259 491.434 487.962 492.461 486.192C492.804 485.4 493.366 485.071 493.963 484.852C494.648 483.546 495.069 482.653 496.325 481.726C499.162 480.04 500.786 480.992 503.508 479.821C511.947 474.596 516.891 466.43 524.618 460.742C524.618 460.742 523.117 456.899 520.64 448.599C520.64 448.599 507.802 424.624 518.138 420.022C518.138 420.022 528.877 421.742 532.627 445.009C532.627 445.009 538.51 460.717 537.861 465.149C533.479 468.664 509.163 485.096 508.768 485.509Z" fill="white"/>
</mask>
<g mask="url(#mask66_2476_449)">
<path d="M491.434 420.022V494.029H538.51V420.022H491.434Z" fill="url(#paint32_linear_2476_449)"/>
</g>
</g>
<mask id="mask67_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="501" y="416" width="39" height="72">
<path d="M501.304 416.365H539.52V487.557H501.304V416.365Z" fill="white"/>
</mask>
<g mask="url(#mask67_2476_449)">
<mask id="mask68_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="503" y="417" width="36" height="69">
<path d="M518.479 417.823C518.479 417.823 529.588 418.009 531.722 440.273C534.277 445.422 538.141 460.793 538.483 465.015C537.227 467.004 509.338 485.56 508.767 485.51C506.493 485.316 503.2 482.105 503.507 479.821C503.762 478.102 523.116 460.683 524.275 460.574C522.633 458.214 518.567 448.548 517.742 444.411C517.768 444.411 508.17 422.391 518.479 417.823Z" fill="white"/>
</mask>
<g mask="url(#mask68_2476_449)">
<path d="M503.2 417.823V485.56H538.483V417.823H503.2Z" fill="url(#paint33_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M503.508 480.394C503.482 480.2 503.482 480.007 503.508 479.821C503.763 478.102 523.117 460.683 524.276 460.574C522.634 458.214 518.568 448.548 517.742 444.411C517.742 444.411 509.901 426.444 516.013 419.593C522.265 435.705 530.87 458.105 531.178 460.548C531.178 460.548 519.788 471.327 510.217 476.771L503.508 480.394Z" fill="#1A1840"/>
<mask id="mask69_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="433" y="379" width="60" height="68">
<path d="M433.864 379.69H492.312V446.568H433.864V379.69Z" fill="white"/>
</mask>
<g mask="url(#mask69_2476_449)">
<mask id="mask70_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="434" y="380" width="58" height="67">
<path d="M449.17 406.825C447.748 401.651 448.942 398.466 448.055 394.117C447.177 389.761 443.199 386.575 439.704 380.912C437.544 381.401 438.879 384.013 442.488 391.64C441.812 391.311 434.479 392.971 434.962 398.415C434.962 398.415 434.901 402.275 437.799 402.797C437.799 402.797 437.403 406.362 441.864 408.401C446.211 417.005 449.197 439.126 457.969 446.129C461.605 445.876 479.053 438.368 484.366 433.008C490.021 430.261 494.394 418.008 487.799 417.578C484.875 417.385 482.434 417.545 479.676 419.205C477.376 420.435 464.704 428.357 462.316 432.52C461.016 423.562 453.622 417.629 449.17 406.825Z" fill="white"/>
</mask>
<g mask="url(#mask70_2476_449)">
<path d="M434.479 380.912V446.129H494.394V380.912H434.479Z" fill="url(#paint34_linear_2476_449)"/>
</g>
</g>
<mask id="mask71_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="483" y="371" width="39" height="46">
<path d="M483.32 371.062H521.536V416.366H483.32V371.062Z" fill="white"/>
</mask>
<g mask="url(#mask71_2476_449)">
<mask id="mask72_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="485" y="371" width="36" height="46">
<path d="M491.408 379.691C491.408 379.691 484.479 384.915 485.217 404.02C485.814 404.643 489.906 406.388 492.321 407.424C492.883 408.57 491.838 413.112 491.153 415.346C491.153 415.346 506.125 417.435 512.035 414.773C512.035 414.773 510.156 409.624 511.324 408.292C511.324 408.292 522.318 391.118 519.587 381.241C519.587 381.241 518.568 371.466 511.008 372.123C511.069 372.123 492.435 368.011 491.408 379.691Z" fill="white"/>
</mask>
<g mask="url(#mask72_2476_449)">
<path d="M484.479 368.011V417.436H522.318V368.011H484.479Z" fill="url(#paint35_linear_2476_449)"/>
</g>
</g>
<mask id="mask73_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="472" y="409" width="57" height="90">
<path d="M472.08 409.894H528.28V498.344H472.08V409.894Z" fill="white"/>
</mask>
<g mask="url(#mask73_2476_449)">
<mask id="mask74_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="472" y="411" width="55" height="88">
<path d="M511.323 411.259C511.323 411.259 499.679 413.054 490.301 411.941C490.301 411.941 489.475 413.408 488.905 414.823C486.437 415.177 482.09 415.616 480.554 416.484C485.383 420.68 482.573 426.343 479.957 431.374C479.359 457.448 473.678 472.557 472.861 493.541C484.505 496.583 506.406 500.426 524.249 496.971C523.599 487.499 520.131 477.673 518.313 468.226C524.135 456.63 532.433 427.152 519.929 417.545C518.225 416.239 516.065 415.506 513.088 413.762C512.403 413.332 512.061 411.672 511.323 411.259Z" fill="white"/>
</mask>
<g mask="url(#mask74_2476_449)">
<path d="M472.861 411.259V500.426H532.433V411.259H472.861Z" fill="url(#paint36_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M482.486 418.961C484.303 422.77 482.064 427.295 479.93 431.374C479.333 457.447 473.652 472.557 472.826 493.541C474.793 494.055 477.032 494.569 479.509 495.091C479.763 480.858 482.407 461.037 482.407 461.037C482.231 452.686 492.4 422.416 492.4 422.416L482.486 418.961Z" fill="#2B2468"/>
<mask id="mask75_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="481" y="366" width="48" height="44">
<path d="M481.072 366.747H528.28V409.894H481.072V366.747Z" fill="white"/>
</mask>
<g mask="url(#mask75_2476_449)">
<mask id="mask76_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="482" y="368" width="45" height="42">
<path d="M487.088 381.756C486.297 381.68 485.244 381.79 484.62 381.95C487.316 382.742 489.362 384.074 490.697 386.416C489.248 388.675 489.538 392.619 489.336 394.793C496.009 387.04 491.32 406.987 497.116 408.377C497.116 408.377 507.205 410.745 511.833 408.023C514.45 401.872 515.53 400.515 518.085 398.113C519.139 397.296 520.158 396.293 521.036 394.961C525.356 388.481 523.565 384.503 523.565 384.503L526.27 380.559L522.485 381.132C523.003 379.253 522.371 376.666 521.299 374.113C521.378 375.309 520.667 376.7 519.956 377.568C506.924 358.219 493.998 376.557 486.578 369.806C487.658 373.919 490.073 375.116 490.073 375.116C486.578 375.849 482.346 372.503 482.346 372.503C482.407 377.079 484.163 379.961 487.088 381.756Z" fill="white"/>
</mask>
<g mask="url(#mask76_2476_449)">
<path d="M482.346 358.22V410.746H526.27V358.22H482.346Z" fill="url(#paint37_linear_2476_449)"/>
</g>
</g>
<mask id="mask77_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="487" y="388" width="8" height="12">
<path d="M487.816 388.32H494.56V399.107H487.816V388.32Z" fill="white"/>
</mask>
<g mask="url(#mask77_2476_449)">
<mask id="mask78_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="489" y="388" width="5" height="11">
<path d="M492.347 388.371C491.469 388.211 490.301 390.301 489.731 393.056C489.169 395.804 489.388 398.172 490.275 398.357C491.153 398.526 492.321 396.427 492.883 393.68C493.454 390.899 493.199 388.531 492.347 388.371Z" fill="white"/>
</mask>
<g mask="url(#mask78_2476_449)">
<path d="M489.169 388.211V398.526H493.454V388.211H489.169Z" fill="url(#paint38_linear_2476_449)"/>
</g>
</g>
<mask id="mask79_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="440" y="405" width="55" height="42">
<path d="M440.608 405.579H494.56V446.568H440.608V405.579Z" fill="white"/>
</mask>
<g mask="url(#mask79_2476_449)">
<mask id="mask80_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="441" y="407" width="52" height="40">
<path d="M489.054 414.823C483.144 413.955 476.321 417.68 473.511 422.585C471.044 424.295 464.985 428.543 462.833 430.995C462.948 429.444 450.162 407.121 449.424 407.399C446.465 408.427 443.655 409.952 441.381 411.368C444.762 423.722 449.337 439.624 458.004 446.071C464.959 444.63 479.395 439.186 484.936 435.133C490.582 431.728 496.72 415.969 489.054 414.823Z" fill="white"/>
</mask>
<g mask="url(#mask80_2476_449)">
<path d="M441.381 407.121V446.071H496.72V407.121H441.381Z" fill="url(#paint39_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M444.902 409.354C443.629 410.011 442.435 410.685 441.354 411.368C444.735 423.722 449.31 439.624 457.969 446.07C464.932 444.629 479.36 439.185 484.901 435.132C487.122 433.792 489.423 430.556 490.925 427.076C474.109 437.635 458.566 441.419 458.566 441.419C454.307 436.135 448.371 419.643 444.902 409.354Z" fill="#221E54"/>
<mask id="mask81_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="487" y="409" width="28" height="8">
<path d="M487.816 409.894H514.792V416.366H487.816V409.894Z" fill="white"/>
</mask>
<g mask="url(#mask81_2476_449)">
<mask id="mask82_2476_449" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="488" y="411" width="26" height="5">
<path d="M492.005 412.076C500.277 412.759 509.647 411.478 511.095 411.259C511.236 411.233 511.297 411.233 511.297 411.233C512.035 411.638 512.378 413.332 513.054 413.711C513.115 413.736 513.168 413.762 513.229 413.821C512.773 413.905 512.29 413.981 511.807 414.065C506.775 414.882 501.129 415.481 495.158 415.776C494.42 415.809 493.656 415.835 492.918 415.86C492.374 415.885 491.865 415.885 491.32 415.919C490.758 415.346 490.02 414.967 489.055 414.823C488.993 414.823 488.94 414.798 488.879 414.798C488.879 414.798 488.879 414.798 488.905 414.798C489.476 413.382 490.301 411.916 490.301 411.916C490.872 411.992 491.434 412.051 492.005 412.076Z" fill="white"/>
</mask>
<g mask="url(#mask82_2476_449)">
<path d="M488.879 411.233V415.919H513.229V411.233H488.879Z" fill="url(#paint40_linear_2476_449)"/>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M447.8 420.048C447.607 419.56 447.036 419.315 446.527 419.509C446.009 419.694 445.754 420.242 445.956 420.731C446.158 421.22 446.72 421.464 447.238 421.279C447.747 421.085 448.002 420.545 447.8 420.048Z" fill="#E4E4F6"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M445.306 413.871C445.104 413.382 444.533 413.137 444.024 413.331C443.515 413.517 443.26 414.064 443.453 414.553C443.655 415.042 444.226 415.286 444.735 415.101C445.245 414.907 445.499 414.359 445.306 413.871Z" fill="#E4E4F6"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M446.553 416.947C446.351 416.458 445.789 416.214 445.271 416.407C444.762 416.593 444.507 417.141 444.709 417.629C444.902 418.118 445.473 418.362 445.982 418.177C446.492 418.009 446.755 417.435 446.553 416.947Z" fill="#E4E4F6"/>
</g>
<defs>
<linearGradient id="paint0_linear_2476_449" x1="447.942" y1="612.286" x2="447.94" y2="500.802" gradientUnits="userSpaceOnUse">
<stop stop-color="#157FC3"/>
<stop offset="3.16824e-05" stop-color="#157FC3"/>
<stop offset="0.125" stop-color="#157FC3"/>
<stop offset="0.1875" stop-color="#157FC3"/>
<stop offset="0.203125" stop-color="#157FC3"/>
<stop offset="0.207031" stop-color="#1680C4"/>
<stop offset="0.210938" stop-color="#1781C5"/>
<stop offset="0.214844" stop-color="#1982C5"/>
<stop offset="0.21875" stop-color="#1A83C6"/>
<stop offset="0.222656" stop-color="#1B85C7"/>
<stop offset="0.226562" stop-color="#1C86C8"/>
<stop offset="0.230469" stop-color="#1D87C8"/>
<stop offset="0.234375" stop-color="#1E88C9"/>
<stop offset="0.238281" stop-color="#2089CA"/>
<stop offset="0.242188" stop-color="#218ACB"/>
<stop offset="0.246094" stop-color="#228BCB"/>
<stop offset="0.25" stop-color="#238CCC"/>
<stop offset="0.253906" stop-color="#248DCD"/>
<stop offset="0.257813" stop-color="#258ECD"/>
<stop offset="0.261719" stop-color="#278FCE"/>
<stop offset="0.265625" stop-color="#2890CF"/>
<stop offset="0.269531" stop-color="#2991D0"/>
<stop offset="0.273438" stop-color="#2A92D0"/>
<stop offset="0.277344" stop-color="#2B93D1"/>
<stop offset="0.28125" stop-color="#2C95D2"/>
<stop offset="0.285156" stop-color="#2D96D3"/>
<stop offset="0.289062" stop-color="#2F97D3"/>
<stop offset="0.292969" stop-color="#3098D4"/>
<stop offset="0.296875" stop-color="#3199D5"/>
<stop offset="0.300781" stop-color="#329AD6"/>
<stop offset="0.304688" stop-color="#339BD6"/>
<stop offset="0.308594" stop-color="#349CD7"/>
<stop offset="0.3125" stop-color="#369DD8"/>
<stop offset="0.316406" stop-color="#379ED8"/>
<stop offset="0.320313" stop-color="#389FD9"/>
<stop offset="0.324219" stop-color="#39A0DA"/>
<stop offset="0.328125" stop-color="#3AA1DB"/>
<stop offset="0.34375" stop-color="#3AA1DB"/>
<stop offset="0.359375" stop-color="#3AA1DA"/>
<stop offset="0.375" stop-color="#39A0DA"/>
<stop offset="0.390625" stop-color="#389FD9"/>
<stop offset="0.40625" stop-color="#379ED9"/>
<stop offset="0.421875" stop-color="#369ED8"/>
<stop offset="0.4375" stop-color="#359DD7"/>
<stop offset="0.453125" stop-color="#349CD7"/>
<stop offset="0.46875" stop-color="#339BD6"/>
<stop offset="0.484375" stop-color="#339AD6"/>
<stop offset="0.5" stop-color="#3299D5"/>
<stop offset="0.515625" stop-color="#3199D5"/>
<stop offset="0.53125" stop-color="#3098D4"/>
<stop offset="0.546875" stop-color="#2F97D4"/>
<stop offset="0.5625" stop-color="#2E96D3"/>
<stop offset="0.578125" stop-color="#2D95D2"/>
<stop offset="0.59375" stop-color="#2C95D2"/>
<stop offset="0.609375" stop-color="#2C94D1"/>
<stop offset="0.625" stop-color="#2B93D1"/>
<stop offset="0.640625" stop-color="#2A92D0"/>
<stop offset="0.65625" stop-color="#2991D0"/>
<stop offset="0.671875" stop-color="#2891CF"/>
<stop offset="0.6875" stop-color="#2790CF"/>
<stop offset="0.703125" stop-color="#268FCE"/>
<stop offset="0.71875" stop-color="#258ECD"/>
<stop offset="0.734375" stop-color="#248DCD"/>
<stop offset="0.75" stop-color="#248CCC"/>
<stop offset="0.765625" stop-color="#238CCC"/>
<stop offset="0.78125" stop-color="#228BCB"/>
<stop offset="0.796875" stop-color="#218ACB"/>
<stop offset="0.8125" stop-color="#2089CA"/>
<stop offset="0.828125" stop-color="#1F88CA"/>
<stop offset="0.84375" stop-color="#1E88C9"/>
<stop offset="0.859375" stop-color="#1D87C8"/>
<stop offset="0.875" stop-color="#1C86C8"/>
<stop offset="0.890625" stop-color="#1C85C7"/>
<stop offset="0.90625" stop-color="#1B84C7"/>
<stop offset="0.921875" stop-color="#1A83C6"/>
<stop offset="0.9375" stop-color="#1983C6"/>
<stop offset="0.953125" stop-color="#1882C5"/>
<stop offset="0.96875" stop-color="#1781C4"/>
<stop offset="0.984375" stop-color="#1680C4"/>
<stop offset="0.999968" stop-color="#157FC3"/>
<stop offset="1" stop-color="#157FC3"/>
</linearGradient>
<linearGradient id="paint1_linear_2476_449" x1="220.339" y1="573.725" x2="305.635" y2="499.762" gradientUnits="userSpaceOnUse">
<stop stop-color="#433B97"/>
<stop offset="0.330984" stop-color="#433B97"/>
<stop offset="0.5" stop-color="#433B97"/>
<stop offset="0.515625" stop-color="#433B97"/>
<stop offset="0.53125" stop-color="#443C97"/>
<stop offset="0.546875" stop-color="#443C98"/>
<stop offset="0.5625" stop-color="#453D98"/>
<stop offset="0.578125" stop-color="#453E98"/>
<stop offset="0.59375" stop-color="#463E99"/>
<stop offset="0.609375" stop-color="#473F99"/>
<stop offset="0.625" stop-color="#474099"/>
<stop offset="0.640625" stop-color="#48409A"/>
<stop offset="0.65625" stop-color="#48419A"/>
<stop offset="0.669016" stop-color="#49429A"/>
<stop offset="0.671875" stop-color="#49429B"/>
<stop offset="0.6875" stop-color="#4A429B"/>
<stop offset="0.703125" stop-color="#4A439B"/>
<stop offset="0.71875" stop-color="#4B449B"/>
<stop offset="0.734375" stop-color="#4B449C"/>
<stop offset="0.75" stop-color="#4C459C"/>
<stop offset="0.765625" stop-color="#4C469C"/>
<stop offset="0.78125" stop-color="#4D469D"/>
<stop offset="0.796875" stop-color="#4E479D"/>
<stop offset="0.8125" stop-color="#4E489D"/>
<stop offset="0.828125" stop-color="#4F489E"/>
<stop offset="0.84375" stop-color="#4F499E"/>
<stop offset="0.859375" stop-color="#504A9E"/>
<stop offset="0.875" stop-color="#504A9F"/>
<stop offset="0.890625" stop-color="#514B9F"/>
<stop offset="0.90625" stop-color="#524C9F"/>
<stop offset="0.921875" stop-color="#524CA0"/>
<stop offset="0.9375" stop-color="#534DA0"/>
<stop offset="0.953125" stop-color="#534EA0"/>
<stop offset="0.96875" stop-color="#544EA1"/>
<stop offset="0.984375" stop-color="#554FA1"/>
<stop offset="1" stop-color="#5550A1"/>
</linearGradient>
<linearGradient id="paint2_linear_2476_449" x1="281.423" y1="444.953" x2="314.333" y2="419.032" gradientUnits="userSpaceOnUse">
<stop stop-color="#F37121"/>
<stop offset="0.25" stop-color="#F37121"/>
<stop offset="0.375" stop-color="#F37121"/>
<stop offset="0.390625" stop-color="#F37121"/>
<stop offset="0.394531" stop-color="#F37221"/>
<stop offset="0.398438" stop-color="#F37321"/>
<stop offset="0.402344" stop-color="#F47521"/>
<stop offset="0.40625" stop-color="#F47621"/>
<stop offset="0.410156" stop-color="#F47821"/>
<stop offset="0.414062" stop-color="#F47A21"/>
<stop offset="0.417969" stop-color="#F47C21"/>
<stop offset="0.421875" stop-color="#F57D21"/>
<stop offset="0.425781" stop-color="#F57F21"/>
<stop offset="0.429688" stop-color="#F58121"/>
<stop offset="0.433594" stop-color="#F58221"/>
<stop offset="0.4375" stop-color="#F68421"/>
<stop offset="0.441406" stop-color="#F68621"/>
<stop offset="0.445312" stop-color="#F68821"/>
<stop offset="0.449219" stop-color="#F68921"/>
<stop offset="0.453125" stop-color="#F78B21"/>
<stop offset="0.457031" stop-color="#F78D21"/>
<stop offset="0.460938" stop-color="#F78F21"/>
<stop offset="0.464844" stop-color="#F79021"/>
<stop offset="0.46875" stop-color="#F79221"/>
<stop offset="0.472656" stop-color="#F89421"/>
<stop offset="0.476562" stop-color="#F89521"/>
<stop offset="0.480469" stop-color="#F89723"/>
<stop offset="0.484375" stop-color="#F89924"/>
<stop offset="0.488281" stop-color="#F89A27"/>
<stop offset="0.492188" stop-color="#F99B2A"/>
<stop offset="0.496094" stop-color="#F99D2D"/>
<stop offset="0.5" stop-color="#F99E30"/>
<stop offset="0.503906" stop-color="#F99F32"/>
<stop offset="0.507812" stop-color="#F9A135"/>
<stop offset="0.511719" stop-color="#F9A238"/>
<stop offset="0.515625" stop-color="#F9A33B"/>
<stop offset="0.519531" stop-color="#F9A53E"/>
<stop offset="0.523438" stop-color="#FAA641"/>
<stop offset="0.527344" stop-color="#FAA743"/>
<stop offset="0.53125" stop-color="#FAA946"/>
<stop offset="0.535156" stop-color="#FAA948"/>
<stop offset="0.539062" stop-color="#FAAA49"/>
<stop offset="0.546875" stop-color="#FAAA49"/>
<stop offset="0.5625" stop-color="#FAAA49"/>
<stop offset="0.604185" stop-color="#FAAA49"/>
<stop offset="0.625" stop-color="#FAAA49"/>
<stop offset="0.75" stop-color="#FAAA49"/>
<stop offset="1" stop-color="#FAAA49"/>
</linearGradient>
<linearGradient id="paint3_linear_2476_449" x1="240.697" y1="490.777" x2="296.975" y2="404.187" gradientUnits="userSpaceOnUse">
<stop stop-color="#F37121"/>
<stop offset="0.125" stop-color="#F37121"/>
<stop offset="0.15625" stop-color="#F37121"/>
<stop offset="0.160156" stop-color="#F37221"/>
<stop offset="0.164062" stop-color="#F37321"/>
<stop offset="0.167969" stop-color="#F37321"/>
<stop offset="0.171875" stop-color="#F37421"/>
<stop offset="0.175781" stop-color="#F37421"/>
<stop offset="0.179688" stop-color="#F47521"/>
<stop offset="0.183594" stop-color="#F47521"/>
<stop offset="0.1875" stop-color="#F47621"/>
<stop offset="0.191406" stop-color="#F47621"/>
<stop offset="0.195312" stop-color="#F47721"/>
<stop offset="0.199219" stop-color="#F47721"/>
<stop offset="0.203125" stop-color="#F47821"/>
<stop offset="0.207031" stop-color="#F47821"/>
<stop offset="0.210937" stop-color="#F47921"/>
<stop offset="0.214844" stop-color="#F47921"/>
<stop offset="0.21875" stop-color="#F47A21"/>
<stop offset="0.222656" stop-color="#F47A21"/>
<stop offset="0.226562" stop-color="#F47B21"/>
<stop offset="0.230469" stop-color="#F47B21"/>
<stop offset="0.234375" stop-color="#F47C21"/>
<stop offset="0.238281" stop-color="#F57C21"/>
<stop offset="0.242188" stop-color="#F57D21"/>
<stop offset="0.246094" stop-color="#F57D21"/>
<stop offset="0.25" stop-color="#F57E21"/>
<stop offset="0.253906" stop-color="#F57E21"/>
<stop offset="0.257812" stop-color="#F57F21"/>
<stop offset="0.261719" stop-color="#F57F21"/>
<stop offset="0.265625" stop-color="#F58021"/>
<stop offset="0.269531" stop-color="#F58021"/>
<stop offset="0.273438" stop-color="#F58121"/>
<stop offset="0.277344" stop-color="#F58121"/>
<stop offset="0.28125" stop-color="#F58221"/>
<stop offset="0.285156" stop-color="#F58221"/>
<stop offset="0.289062" stop-color="#F58321"/>
<stop offset="0.292969" stop-color="#F58321"/>
<stop offset="0.296875" stop-color="#F68421"/>
<stop offset="0.300781" stop-color="#F68421"/>
<stop offset="0.304688" stop-color="#F68521"/>
<stop offset="0.308594" stop-color="#F68521"/>
<stop offset="0.3125" stop-color="#F68621"/>
<stop offset="0.316406" stop-color="#F68621"/>
<stop offset="0.320312" stop-color="#F68721"/>
<stop offset="0.324219" stop-color="#F68721"/>
<stop offset="0.328125" stop-color="#F68821"/>
<stop offset="0.332031" stop-color="#F68821"/>
<stop offset="0.335937" stop-color="#F68921"/>
<stop offset="0.339844" stop-color="#F68921"/>
<stop offset="0.34375" stop-color="#F68A21"/>
<stop offset="0.347656" stop-color="#F68A21"/>
<stop offset="0.351562" stop-color="#F78B21"/>
<stop offset="0.355469" stop-color="#F78B21"/>
<stop offset="0.359375" stop-color="#F78C21"/>
<stop offset="0.363281" stop-color="#F78C21"/>
<stop offset="0.367188" stop-color="#F78D21"/>
<stop offset="0.371094" stop-color="#F78E21"/>
<stop offset="0.375" stop-color="#F78E21"/>
<stop offset="0.378906" stop-color="#F78F21"/>
<stop offset="0.382812" stop-color="#F78F21"/>
<stop offset="0.386719" stop-color="#F79021"/>
<stop offset="0.390625" stop-color="#F79021"/>
<stop offset="0.394531" stop-color="#F79121"/>
<stop offset="0.398438" stop-color="#F79121"/>
<stop offset="0.402344" stop-color="#F79221"/>
<stop offset="0.40625" stop-color="#F79221"/>
<stop offset="0.410156" stop-color="#F89321"/>
<stop offset="0.414062" stop-color="#F89321"/>
<stop offset="0.417969" stop-color="#F89421"/>
<stop offset="0.421875" stop-color="#F89421"/>
<stop offset="0.425781" stop-color="#F89521"/>
<stop offset="0.429688" stop-color="#F89521"/>
<stop offset="0.433594" stop-color="#F89621"/>
<stop offset="0.4375" stop-color="#F89621"/>
<stop offset="0.445312" stop-color="#F89721"/>
<stop offset="0.453125" stop-color="#F89721"/>
<stop offset="0.46875" stop-color="#F89822"/>
<stop offset="0.484375" stop-color="#F89823"/>
<stop offset="0.5" stop-color="#F89924"/>
<stop offset="0.515625" stop-color="#F89A25"/>
<stop offset="0.53125" stop-color="#F99A26"/>
<stop offset="0.546875" stop-color="#F99B27"/>
<stop offset="0.5625" stop-color="#F99C28"/>
<stop offset="0.578125" stop-color="#F99C29"/>
<stop offset="0.585938" stop-color="#F99D2A"/>
<stop offset="0.59375" stop-color="#F99D2A"/>
<stop offset="0.597656" stop-color="#F99E2B"/>
<stop offset="0.601562" stop-color="#F99E2C"/>
<stop offset="0.605469" stop-color="#F99E2C"/>
<stop offset="0.609375" stop-color="#F99E2D"/>
<stop offset="0.613281" stop-color="#F99F2E"/>
<stop offset="0.617188" stop-color="#F99F2E"/>
<stop offset="0.621094" stop-color="#F99F2F"/>
<stop offset="0.625" stop-color="#F99F2F"/>
<stop offset="0.628906" stop-color="#F9A030"/>
<stop offset="0.632812" stop-color="#F9A031"/>
<stop offset="0.636719" stop-color="#F9A031"/>
<stop offset="0.640625" stop-color="#F9A032"/>
<stop offset="0.644531" stop-color="#F9A132"/>
<stop offset="0.648438" stop-color="#F9A133"/>
<stop offset="0.652344" stop-color="#F9A134"/>
<stop offset="0.65625" stop-color="#F9A134"/>
<stop offset="0.660156" stop-color="#F9A235"/>
<stop offset="0.664062" stop-color="#F9A236"/>
<stop offset="0.667969" stop-color="#F9A236"/>
<stop offset="0.671875" stop-color="#F9A237"/>
<stop offset="0.675781" stop-color="#F9A337"/>
<stop offset="0.679688" stop-color="#F9A338"/>
<stop offset="0.683594" stop-color="#F9A339"/>
<stop offset="0.6875" stop-color="#F9A339"/>
<stop offset="0.691406" stop-color="#F9A43A"/>
<stop offset="0.695312" stop-color="#F9A43A"/>
<stop offset="0.699219" stop-color="#F9A43B"/>
<stop offset="0.703125" stop-color="#F9A43C"/>
<stop offset="0.707031" stop-color="#F9A53C"/>
<stop offset="0.710938" stop-color="#F9A53D"/>
<stop offset="0.714844" stop-color="#FAA53D"/>
<stop offset="0.71875" stop-color="#FAA63E"/>
<stop offset="0.722656" stop-color="#FAA63F"/>
<stop offset="0.726562" stop-color="#FAA63F"/>
<stop offset="0.730469" stop-color="#FAA640"/>
<stop offset="0.734375" stop-color="#FAA741"/>
<stop offset="0.738281" stop-color="#FAA741"/>
<stop offset="0.742188" stop-color="#FAA742"/>
<stop offset="0.746094" stop-color="#FAA742"/>
<stop offset="0.75" stop-color="#FAA843"/>
<stop offset="0.753906" stop-color="#FAA844"/>
<stop offset="0.757812" stop-color="#FAA844"/>
<stop offset="0.761719" stop-color="#FAA845"/>
<stop offset="0.765625" stop-color="#FAA945"/>
<stop offset="0.769531" stop-color="#FAA946"/>
<stop offset="0.773438" stop-color="#FAA947"/>
<stop offset="0.777344" stop-color="#FAA947"/>
<stop offset="0.78125" stop-color="#FAAA48"/>
<stop offset="0.785156" stop-color="#FAAA48"/>
<stop offset="0.789062" stop-color="#FAAA49"/>
<stop offset="0.796875" stop-color="#FAAA49"/>
<stop offset="0.8125" stop-color="#FAAA49"/>
<stop offset="0.875" stop-color="#FAAA49"/>
<stop offset="1" stop-color="#FAAA49"/>
</linearGradient>
<linearGradient id="paint4_linear_2476_449" x1="234.406" y1="481.018" x2="283.939" y2="455.878" gradientUnits="userSpaceOnUse">
<stop stop-color="#EBA262"/>
<stop offset="0.25" stop-color="#EBA262"/>
<stop offset="0.34283" stop-color="#EBA262"/>
<stop offset="0.375" stop-color="#EBA262"/>
<stop offset="0.378906" stop-color="#ECA465"/>
<stop offset="0.382812" stop-color="#EDA668"/>
<stop offset="0.386719" stop-color="#EDA86A"/>
<stop offset="0.390625" stop-color="#EEAA6D"/>
<stop offset="0.394531" stop-color="#EFAC6F"/>
<stop offset="0.398437" stop-color="#F0AD72"/>
<stop offset="0.402344" stop-color="#F0AF75"/>
<stop offset="0.40625" stop-color="#F1B177"/>
<stop offset="0.410156" stop-color="#F2B37A"/>
<stop offset="0.414062" stop-color="#F3B57C"/>
<stop offset="0.417969" stop-color="#F3B77F"/>
<stop offset="0.421875" stop-color="#F4B882"/>
<stop offset="0.425781" stop-color="#F5BA84"/>
<stop offset="0.429688" stop-color="#F6BC87"/>
<stop offset="0.433594" stop-color="#F6BE89"/>
<stop offset="0.4375" stop-color="#F7C08C"/>
<stop offset="0.441406" stop-color="#F8C28E"/>
<stop offset="0.445312" stop-color="#F9C491"/>
<stop offset="0.449219" stop-color="#F9C594"/>
<stop offset="0.453125" stop-color="#FAC796"/>
<stop offset="0.457031" stop-color="#FBC999"/>
<stop offset="0.460937" stop-color="#FCCB9B"/>
<stop offset="0.464844" stop-color="#FCCD9E"/>
<stop offset="0.46875" stop-color="#FDCFA1"/>
<stop offset="0.472656" stop-color="#FED0A2"/>
<stop offset="0.476562" stop-color="#FED1A4"/>
<stop offset="0.484375" stop-color="#FED1A4"/>
<stop offset="0.5" stop-color="#FED1A4"/>
<stop offset="0.65717" stop-color="#FED1A4"/>
<stop offset="1" stop-color="#FED1A4"/>
</linearGradient>
<linearGradient id="paint5_linear_2476_449" x1="232.396" y1="442.993" x2="267.771" y2="424.911" gradientUnits="userSpaceOnUse">
<stop stop-color="#F89721"/>
<stop offset="0.25" stop-color="#F89721"/>
<stop offset="0.3125" stop-color="#F89721"/>
<stop offset="0.34375" stop-color="#F89721"/>
<stop offset="0.359375" stop-color="#F89721"/>
<stop offset="0.363281" stop-color="#F89822"/>
<stop offset="0.367188" stop-color="#F89823"/>
<stop offset="0.371094" stop-color="#F89925"/>
<stop offset="0.375" stop-color="#F89926"/>
<stop offset="0.378906" stop-color="#F89A27"/>
<stop offset="0.382812" stop-color="#F89B28"/>
<stop offset="0.386719" stop-color="#F89B2A"/>
<stop offset="0.390625" stop-color="#F99C2B"/>
<stop offset="0.394531" stop-color="#F99C2C"/>
<stop offset="0.398438" stop-color="#F99D2D"/>
<stop offset="0.402344" stop-color="#F99E2F"/>
<stop offset="0.40625" stop-color="#F99E30"/>
<stop offset="0.410156" stop-color="#F99F31"/>
<stop offset="0.414062" stop-color="#F99F32"/>
<stop offset="0.417969" stop-color="#F9A034"/>
<stop offset="0.421875" stop-color="#F9A135"/>
<stop offset="0.425781" stop-color="#F9A136"/>
<stop offset="0.429687" stop-color="#F9A237"/>
<stop offset="0.433594" stop-color="#F9A239"/>
<stop offset="0.4375" stop-color="#F9A33A"/>
<stop offset="0.441406" stop-color="#F9A43B"/>
<stop offset="0.445312" stop-color="#F9A43D"/>
<stop offset="0.449219" stop-color="#F9A53E"/>
<stop offset="0.453125" stop-color="#F9A53F"/>
<stop offset="0.457031" stop-color="#FAA640"/>
<stop offset="0.460937" stop-color="#FAA742"/>
<stop offset="0.464844" stop-color="#FAA743"/>
<stop offset="0.46875" stop-color="#FAA844"/>
<stop offset="0.472656" stop-color="#FAA845"/>
<stop offset="0.476562" stop-color="#FAA947"/>
<stop offset="0.480469" stop-color="#FAAA48"/>
<stop offset="0.484375" stop-color="#FAAA49"/>
<stop offset="0.5" stop-color="#FAAA49"/>
<stop offset="0.531665" stop-color="#FAAA49"/>
<stop offset="1" stop-color="#FAAA49"/>
</linearGradient>
<linearGradient id="paint6_linear_2476_449" x1="323.432" y1="489.712" x2="261.355" y2="483.054" gradientUnits="userSpaceOnUse">
<stop stop-color="#6968A9"/>
<stop offset="0.0114358" stop-color="#6968A9"/>
<stop offset="0.5" stop-color="#6968A9"/>
<stop offset="0.625" stop-color="#6968A9"/>
<stop offset="0.6875" stop-color="#6968A9"/>
<stop offset="0.703125" stop-color="#6968A9"/>
<stop offset="0.707031" stop-color="#6A69AA"/>
<stop offset="0.710938" stop-color="#6B6AAA"/>
<stop offset="0.714844" stop-color="#6C6BAB"/>
<stop offset="0.71875" stop-color="#6D6CAC"/>
<stop offset="0.722656" stop-color="#6E6DAD"/>
<stop offset="0.726562" stop-color="#6F6EAD"/>
<stop offset="0.730469" stop-color="#706FAE"/>
<stop offset="0.734375" stop-color="#7170AF"/>
<stop offset="0.738281" stop-color="#7272AF"/>
<stop offset="0.742188" stop-color="#7373B0"/>
<stop offset="0.746094" stop-color="#7474B1"/>
<stop offset="0.75" stop-color="#7575B1"/>
<stop offset="0.753906" stop-color="#7676B2"/>
<stop offset="0.757812" stop-color="#7777B3"/>
<stop offset="0.761719" stop-color="#7878B3"/>
<stop offset="0.765625" stop-color="#7879B4"/>
<stop offset="0.769531" stop-color="#797AB5"/>
<stop offset="0.773438" stop-color="#7A7BB6"/>
<stop offset="0.777344" stop-color="#7B7CB6"/>
<stop offset="0.78125" stop-color="#7C7DB7"/>
<stop offset="0.785156" stop-color="#7D7EB8"/>
<stop offset="0.789062" stop-color="#7E7FB8"/>
<stop offset="0.792969" stop-color="#7F80B9"/>
<stop offset="0.796875" stop-color="#8081BA"/>
<stop offset="0.800781" stop-color="#8182BA"/>
<stop offset="0.804688" stop-color="#8283BB"/>
<stop offset="0.808594" stop-color="#8384BC"/>
<stop offset="0.8125" stop-color="#8485BD"/>
<stop offset="0.816406" stop-color="#8586BD"/>
<stop offset="0.820312" stop-color="#8688BE"/>
<stop offset="0.824219" stop-color="#8789BF"/>
<stop offset="0.828125" stop-color="#888ABF"/>
<stop offset="0.832031" stop-color="#898BC0"/>
<stop offset="0.835938" stop-color="#8A8CC1"/>
<stop offset="0.839844" stop-color="#8B8DC1"/>
<stop offset="0.84375" stop-color="#8C8EC2"/>
<stop offset="0.847656" stop-color="#8D8FC3"/>
<stop offset="0.851562" stop-color="#8E90C3"/>
<stop offset="0.855469" stop-color="#8F91C4"/>
<stop offset="0.859375" stop-color="#9092C5"/>
<stop offset="0.863281" stop-color="#9193C6"/>
<stop offset="0.867187" stop-color="#9194C6"/>
<stop offset="0.871094" stop-color="#9295C7"/>
<stop offset="0.875" stop-color="#9396C8"/>
<stop offset="0.878906" stop-color="#9497C8"/>
<stop offset="0.882812" stop-color="#9598C9"/>
<stop offset="0.886719" stop-color="#9699CA"/>
<stop offset="0.890625" stop-color="#979ACA"/>
<stop offset="0.894531" stop-color="#989BCB"/>
<stop offset="0.898437" stop-color="#999DCC"/>
<stop offset="0.902344" stop-color="#9A9ECC"/>
<stop offset="0.90625" stop-color="#9B9FCD"/>
<stop offset="0.910156" stop-color="#9CA0CE"/>
<stop offset="0.914062" stop-color="#9DA1CF"/>
<stop offset="0.917969" stop-color="#9EA2CF"/>
<stop offset="0.921875" stop-color="#9FA3D0"/>
<stop offset="0.925781" stop-color="#A0A4D1"/>
<stop offset="0.929687" stop-color="#A1A5D1"/>
<stop offset="0.933594" stop-color="#A2A6D2"/>
<stop offset="0.9375" stop-color="#A3A7D3"/>
<stop offset="0.941406" stop-color="#A4A8D3"/>
<stop offset="0.945312" stop-color="#A5A9D4"/>
<stop offset="0.949219" stop-color="#A6AAD5"/>
<stop offset="0.953125" stop-color="#A7ABD6"/>
<stop offset="0.957031" stop-color="#A8ACD6"/>
<stop offset="0.960937" stop-color="#A9ADD7"/>
<stop offset="0.964844" stop-color="#AAAED8"/>
<stop offset="0.96875" stop-color="#ABAFD8"/>
<stop offset="0.972656" stop-color="#ABB0D9"/>
<stop offset="0.976562" stop-color="#ACB1DA"/>
<stop offset="0.984375" stop-color="#ADB2DA"/>
<stop offset="0.988564" stop-color="#ADB2DA"/>
<stop offset="1" stop-color="#ADB2DA"/>
</linearGradient>
<linearGradient id="paint7_linear_2476_449" x1="262.8" y1="493.43" x2="267.999" y2="484.492" gradientUnits="userSpaceOnUse">
<stop stop-color="#56569C"/>
<stop offset="0.25" stop-color="#56569C"/>
<stop offset="0.375" stop-color="#56569C"/>
<stop offset="0.412995" stop-color="#56569C"/>
<stop offset="0.4375" stop-color="#56569C"/>
<stop offset="0.46875" stop-color="#56569C"/>
<stop offset="0.484375" stop-color="#56569C"/>
<stop offset="0.492188" stop-color="#56569C"/>
<stop offset="0.496094" stop-color="#59599E"/>
<stop offset="0.5" stop-color="#5D5DA0"/>
<stop offset="0.503906" stop-color="#6666A6"/>
<stop offset="0.507812" stop-color="#6F6FAC"/>
<stop offset="0.511719" stop-color="#7879B2"/>
<stop offset="0.515625" stop-color="#8182B8"/>
<stop offset="0.519531" stop-color="#8A8BBE"/>
<stop offset="0.523438" stop-color="#9494C4"/>
<stop offset="0.527344" stop-color="#9D9ECA"/>
<stop offset="0.53125" stop-color="#A6A7D0"/>
<stop offset="0.535156" stop-color="#AFB0D6"/>
<stop offset="0.539063" stop-color="#B8BADC"/>
<stop offset="0.542969" stop-color="#BFC0E0"/>
<stop offset="0.546875" stop-color="#C5C7E4"/>
<stop offset="0.5625" stop-color="#C5C7E4"/>
<stop offset="0.587005" stop-color="#C5C7E4"/>
<stop offset="0.625" stop-color="#C5C7E4"/>
<stop offset="0.75" stop-color="#C5C7E4"/>
<stop offset="1" stop-color="#C5C7E4"/>
</linearGradient>
<linearGradient id="paint8_linear_2476_449" x1="296.767" y1="493.668" x2="296.448" y2="485.951" gradientUnits="userSpaceOnUse">
<stop stop-color="#56569C"/>
<stop offset="0.25" stop-color="#56569C"/>
<stop offset="0.288823" stop-color="#56569C"/>
<stop offset="0.3125" stop-color="#56569C"/>
<stop offset="0.320312" stop-color="#56569C"/>
<stop offset="0.324219" stop-color="#59599E"/>
<stop offset="0.328125" stop-color="#5B5B9F"/>
<stop offset="0.332031" stop-color="#5D5EA1"/>
<stop offset="0.335938" stop-color="#6060A2"/>
<stop offset="0.339844" stop-color="#6262A4"/>
<stop offset="0.34375" stop-color="#6464A5"/>
<stop offset="0.347656" stop-color="#6667A7"/>
<stop offset="0.351562" stop-color="#6869A8"/>
<stop offset="0.355469" stop-color="#6B6BA9"/>
<stop offset="0.359375" stop-color="#6D6DAB"/>
<stop offset="0.363281" stop-color="#6F6FAC"/>
<stop offset="0.367188" stop-color="#7172AE"/>
<stop offset="0.371094" stop-color="#7374AF"/>
<stop offset="0.375" stop-color="#7676B1"/>
<stop offset="0.378906" stop-color="#7878B2"/>
<stop offset="0.382812" stop-color="#7A7BB3"/>
<stop offset="0.386719" stop-color="#7C7DB5"/>
<stop offset="0.390625" stop-color="#7E7FB6"/>
<stop offset="0.394531" stop-color="#8181B8"/>
<stop offset="0.398438" stop-color="#8384B9"/>
<stop offset="0.402344" stop-color="#8586BB"/>
<stop offset="0.40625" stop-color="#8788BC"/>
<stop offset="0.410156" stop-color="#898ABD"/>
<stop offset="0.414062" stop-color="#8C8DBF"/>
<stop offset="0.417969" stop-color="#8E8FC0"/>
<stop offset="0.421875" stop-color="#9091C2"/>
<stop offset="0.425781" stop-color="#9293C3"/>
<stop offset="0.429687" stop-color="#9495C5"/>
<stop offset="0.433594" stop-color="#9798C6"/>
<stop offset="0.4375" stop-color="#999AC7"/>
<stop offset="0.441406" stop-color="#9B9CC9"/>
<stop offset="0.445312" stop-color="#9D9ECA"/>
<stop offset="0.449219" stop-color="#9FA1CC"/>
<stop offset="0.453125" stop-color="#A2A3CD"/>
<stop offset="0.457031" stop-color="#A4A5CE"/>
<stop offset="0.460938" stop-color="#A6A7D0"/>
<stop offset="0.464844" stop-color="#A8AAD1"/>
<stop offset="0.46875" stop-color="#AAACD3"/>
<stop offset="0.472656" stop-color="#ADAED4"/>
<stop offset="0.476562" stop-color="#AFB0D6"/>
<stop offset="0.480469" stop-color="#B1B3D7"/>
<stop offset="0.484375" stop-color="#B3B5D8"/>
<stop offset="0.488281" stop-color="#B5B7DA"/>
<stop offset="0.492188" stop-color="#B8B9DB"/>
<stop offset="0.496094" stop-color="#BABCDD"/>
<stop offset="0.5" stop-color="#BCBEDE"/>
<stop offset="0.503906" stop-color="#BEC0E0"/>
<stop offset="0.507812" stop-color="#C1C2E1"/>
<stop offset="0.511719" stop-color="#C3C4E2"/>
<stop offset="0.515625" stop-color="#C5C7E4"/>
<stop offset="0.53125" stop-color="#C5C7E4"/>
<stop offset="0.5625" stop-color="#C5C7E4"/>
<stop offset="0.625" stop-color="#C5C7E4"/>
<stop offset="0.711177" stop-color="#C5C7E4"/>
<stop offset="0.75" stop-color="#C5C7E4"/>
<stop offset="1" stop-color="#C5C7E4"/>
</linearGradient>
<linearGradient id="paint9_linear_2476_449" x1="288.927" y1="474.8" x2="263.349" y2="466.314" gradientUnits="userSpaceOnUse">
<stop stop-color="#8A8DC2"/>
<stop offset="0.44172" stop-color="#8A8DC2"/>
<stop offset="0.5" stop-color="#8A8DC2"/>
<stop offset="0.503906" stop-color="#9295C7"/>
<stop offset="0.507812" stop-color="#9A9DCB"/>
<stop offset="0.511719" stop-color="#B6B7DB"/>
<stop offset="0.515625" stop-color="#D1D2EA"/>
<stop offset="0.53125" stop-color="#D1D2EA"/>
<stop offset="0.55828" stop-color="#D1D2EA"/>
<stop offset="0.5625" stop-color="#D1D2EA"/>
<stop offset="0.625" stop-color="#D1D2EA"/>
<stop offset="0.75" stop-color="#D1D2EA"/>
<stop offset="1" stop-color="#D1D2EA"/>
</linearGradient>
<linearGradient id="paint10_linear_2476_449" x1="286.194" y1="498.992" x2="316.823" y2="441.041" gradientUnits="userSpaceOnUse">
<stop stop-color="#9C9FD0"/>
<stop offset="0.25" stop-color="#9C9FD0"/>
<stop offset="0.265625" stop-color="#9C9FD0"/>
<stop offset="0.269531" stop-color="#9DA0D0"/>
<stop offset="0.273437" stop-color="#9DA0D1"/>
<stop offset="0.277344" stop-color="#9EA1D1"/>
<stop offset="0.28125" stop-color="#9FA2D1"/>
<stop offset="0.285156" stop-color="#9FA2D2"/>
<stop offset="0.289062" stop-color="#A0A3D2"/>
<stop offset="0.292969" stop-color="#A0A3D2"/>
<stop offset="0.296875" stop-color="#A1A4D3"/>
<stop offset="0.300781" stop-color="#A2A5D3"/>
<stop offset="0.304687" stop-color="#A2A5D3"/>
<stop offset="0.308594" stop-color="#A3A6D3"/>
<stop offset="0.3125" stop-color="#A4A6D4"/>
<stop offset="0.316406" stop-color="#A4A7D4"/>
<stop offset="0.320312" stop-color="#A5A8D4"/>
<stop offset="0.324219" stop-color="#A5A8D5"/>
<stop offset="0.328125" stop-color="#A6A9D5"/>
<stop offset="0.332031" stop-color="#A7A9D5"/>
<stop offset="0.335938" stop-color="#A7AAD5"/>
<stop offset="0.339844" stop-color="#A8ABD6"/>
<stop offset="0.34375" stop-color="#A9ABD6"/>
<stop offset="0.347656" stop-color="#A9ACD6"/>
<stop offset="0.351562" stop-color="#AAACD7"/>
<stop offset="0.355469" stop-color="#AAADD7"/>
<stop offset="0.359375" stop-color="#ABAED7"/>
<stop offset="0.363281" stop-color="#ACAED8"/>
<stop offset="0.367187" stop-color="#ACAFD8"/>
<stop offset="0.371094" stop-color="#ADB0D8"/>
<stop offset="0.375" stop-color="#AEB0D8"/>
<stop offset="0.378906" stop-color="#AEB1D9"/>
<stop offset="0.382812" stop-color="#AFB1D9"/>
<stop offset="0.386719" stop-color="#AFB2D9"/>
<stop offset="0.390625" stop-color="#B0B3DA"/>
<stop offset="0.394531" stop-color="#B1B3DA"/>
<stop offset="0.398438" stop-color="#B1B4DA"/>
<stop offset="0.402344" stop-color="#B2B4DB"/>
<stop offset="0.40625" stop-color="#B3B5DB"/>
<stop offset="0.410156" stop-color="#B3B6DB"/>
<stop offset="0.414062" stop-color="#B4B6DB"/>
<stop offset="0.417969" stop-color="#B4B7DC"/>
<stop offset="0.421875" stop-color="#B5B7DC"/>
<stop offset="0.425781" stop-color="#B6B8DC"/>
<stop offset="0.429688" stop-color="#B6B9DD"/>
<stop offset="0.433594" stop-color="#B7B9DD"/>
<stop offset="0.4375" stop-color="#B8BADD"/>
<stop offset="0.441406" stop-color="#B8BADE"/>
<stop offset="0.445312" stop-color="#B9BBDE"/>
<stop offset="0.449219" stop-color="#B9BCDE"/>
<stop offset="0.453125" stop-color="#BABCDE"/>
<stop offset="0.457031" stop-color="#BBBDDF"/>
<stop offset="0.460937" stop-color="#BBBDDF"/>
<stop offset="0.464844" stop-color="#BCBEDF"/>
<stop offset="0.46875" stop-color="#BCBFE0"/>
<stop offset="0.472656" stop-color="#BDBFE0"/>
<stop offset="0.476562" stop-color="#BEC0E0"/>
<stop offset="0.480469" stop-color="#BEC0E1"/>
<stop offset="0.484375" stop-color="#BFC1E1"/>
<stop offset="0.488281" stop-color="#C0C2E1"/>
<stop offset="0.492188" stop-color="#C0C2E1"/>
<stop offset="0.496094" stop-color="#C1C3E2"/>
<stop offset="0.5" stop-color="#C1C3E2"/>
<stop offset="0.503906" stop-color="#C2C4E2"/>
<stop offset="0.507812" stop-color="#C3C5E3"/>
<stop offset="0.511719" stop-color="#C3C5E3"/>
<stop offset="0.515625" stop-color="#C4C6E3"/>
<stop offset="0.519531" stop-color="#C5C6E4"/>
<stop offset="0.523438" stop-color="#C5C7E4"/>
<stop offset="0.527344" stop-color="#C6C8E4"/>
<stop offset="0.53125" stop-color="#C6C8E4"/>
<stop offset="0.535156" stop-color="#C7C9E5"/>
<stop offset="0.539062" stop-color="#C8C9E5"/>
<stop offset="0.542969" stop-color="#C8CAE5"/>
<stop offset="0.546875" stop-color="#C9CBE6"/>
<stop offset="0.550781" stop-color="#CACBE6"/>
<stop offset="0.554687" stop-color="#CACCE6"/>
<stop offset="0.558594" stop-color="#CBCDE7"/>
<stop offset="0.5625" stop-color="#CBCDE7"/>
<stop offset="0.566406" stop-color="#CCCEE7"/>
<stop offset="0.570312" stop-color="#CDCEE7"/>
<stop offset="0.574219" stop-color="#CDCFE8"/>
<stop offset="0.578125" stop-color="#CED0E8"/>
<stop offset="0.582031" stop-color="#CFD0E8"/>
<stop offset="0.585937" stop-color="#CFD1E9"/>
<stop offset="0.589844" stop-color="#D0D1E9"/>
<stop offset="0.59375" stop-color="#D0D2E9"/>
<stop offset="0.597656" stop-color="#D1D3EA"/>
<stop offset="0.601562" stop-color="#D2D3EA"/>
<stop offset="0.605469" stop-color="#D2D4EA"/>
<stop offset="0.609375" stop-color="#D3D4EA"/>
<stop offset="0.613281" stop-color="#D4D5EB"/>
<stop offset="0.617188" stop-color="#D4D6EB"/>
<stop offset="0.621094" stop-color="#D5D6EB"/>
<stop offset="0.625" stop-color="#D5D7EC"/>
<stop offset="0.628906" stop-color="#D6D7EC"/>
<stop offset="0.632812" stop-color="#D7D8EC"/>
<stop offset="0.636719" stop-color="#D7D9ED"/>
<stop offset="0.640625" stop-color="#D8D9ED"/>
<stop offset="0.644531" stop-color="#D8DAED"/>
<stop offset="0.648438" stop-color="#D9DAED"/>
<stop offset="0.652344" stop-color="#DADBEE"/>
<stop offset="0.65625" stop-color="#DADCEE"/>
<stop offset="0.660156" stop-color="#DBDCEE"/>
<stop offset="0.664062" stop-color="#DCDDEF"/>
<stop offset="0.667969" stop-color="#DCDDEF"/>
<stop offset="0.671875" stop-color="#DDDEEF"/>
<stop offset="0.675781" stop-color="#DDDFF0"/>
<stop offset="0.679687" stop-color="#DEDFF0"/>
<stop offset="0.683594" stop-color="#DFE0F0"/>
<stop offset="0.6875" stop-color="#DFE0F0"/>
<stop offset="0.691406" stop-color="#E0E1F1"/>
<stop offset="0.695312" stop-color="#E1E2F1"/>
<stop offset="0.699219" stop-color="#E1E2F1"/>
<stop offset="0.703125" stop-color="#E2E3F2"/>
<stop offset="0.707031" stop-color="#E2E3F2"/>
<stop offset="0.710938" stop-color="#E3E4F2"/>
<stop offset="0.714844" stop-color="#E4E5F3"/>
<stop offset="0.71875" stop-color="#E4E5F3"/>
<stop offset="0.722656" stop-color="#E5E6F3"/>
<stop offset="0.726562" stop-color="#E6E6F3"/>
<stop offset="0.730469" stop-color="#E6E7F4"/>
<stop offset="0.734375" stop-color="#E7E8F4"/>
<stop offset="0.738281" stop-color="#E7E8F4"/>
<stop offset="0.742187" stop-color="#E8E9F5"/>
<stop offset="0.746094" stop-color="#E9EAF5"/>
<stop offset="0.75" stop-color="#E9EAF5"/>
<stop offset="0.753906" stop-color="#EAEBF6"/>
<stop offset="0.757812" stop-color="#EBEBF6"/>
<stop offset="0.765625" stop-color="#EBECF6"/>
<stop offset="0.78125" stop-color="#EBECF6"/>
<stop offset="0.8125" stop-color="#EBECF6"/>
<stop offset="0.875" stop-color="#EBECF6"/>
<stop offset="1" stop-color="#EBECF6"/>
</linearGradient>
<linearGradient id="paint11_linear_2476_449" x1="286.473" y1="498.592" x2="316.596" y2="441.355" gradientUnits="userSpaceOnUse">
<stop stop-color="#ADB2DA"/>
<stop offset="0.25" stop-color="#ADB2DA"/>
<stop offset="0.265625" stop-color="#ADB2DA"/>
<stop offset="0.273438" stop-color="#ADB2DA"/>
<stop offset="0.28125" stop-color="#AEB3DB"/>
<stop offset="0.289062" stop-color="#AFB4DB"/>
<stop offset="0.296875" stop-color="#AFB4DB"/>
<stop offset="0.304688" stop-color="#B0B5DC"/>
<stop offset="0.3125" stop-color="#B1B6DC"/>
<stop offset="0.320312" stop-color="#B2B6DC"/>
<stop offset="0.328125" stop-color="#B2B7DD"/>
<stop offset="0.335938" stop-color="#B3B8DD"/>
<stop offset="0.34375" stop-color="#B4B8DD"/>
<stop offset="0.351562" stop-color="#B5B9DD"/>
<stop offset="0.359375" stop-color="#B5BADE"/>
<stop offset="0.367187" stop-color="#B6BADE"/>
<stop offset="0.375" stop-color="#B7BBDE"/>
<stop offset="0.382812" stop-color="#B7BCDF"/>
<stop offset="0.390625" stop-color="#B8BDDF"/>
<stop offset="0.398437" stop-color="#B9BDDF"/>
<stop offset="0.40625" stop-color="#BABEE0"/>
<stop offset="0.414062" stop-color="#BABFE0"/>
<stop offset="0.421875" stop-color="#BBBFE0"/>
<stop offset="0.429688" stop-color="#BCC0E1"/>
<stop offset="0.4375" stop-color="#BDC1E1"/>
<stop offset="0.445312" stop-color="#BDC1E1"/>
<stop offset="0.453125" stop-color="#BEC2E2"/>
<stop offset="0.460938" stop-color="#BFC3E2"/>
<stop offset="0.462295" stop-color="#BFC3E2"/>
<stop offset="0.46875" stop-color="#C0C4E3"/>
<stop offset="0.476562" stop-color="#C0C4E3"/>
<stop offset="0.484375" stop-color="#C1C5E3"/>
<stop offset="0.492187" stop-color="#C2C5E3"/>
<stop offset="0.5" stop-color="#C2C6E4"/>
<stop offset="0.507812" stop-color="#C3C7E4"/>
<stop offset="0.515625" stop-color="#C4C7E4"/>
<stop offset="0.523438" stop-color="#C4C8E5"/>
<stop offset="0.53125" stop-color="#C5C9E5"/>
<stop offset="0.537705" stop-color="#C6C9E5"/>
<stop offset="0.539062" stop-color="#C6CAE6"/>
<stop offset="0.546875" stop-color="#C7CAE6"/>
<stop offset="0.554687" stop-color="#C7CBE6"/>
<stop offset="0.5625" stop-color="#C8CBE6"/>
<stop offset="0.570312" stop-color="#C9CCE7"/>
<stop offset="0.578125" stop-color="#CACDE7"/>
<stop offset="0.585938" stop-color="#CACDE7"/>
<stop offset="0.59375" stop-color="#CBCEE8"/>
<stop offset="0.601562" stop-color="#CCCFE8"/>
<stop offset="0.609375" stop-color="#CCCFE8"/>
<stop offset="0.617188" stop-color="#CDD0E9"/>
<stop offset="0.625" stop-color="#CED1E9"/>
<stop offset="0.632812" stop-color="#CFD2E9"/>
<stop offset="0.640625" stop-color="#CFD2EA"/>
<stop offset="0.648438" stop-color="#D0D3EA"/>
<stop offset="0.65625" stop-color="#D1D4EA"/>
<stop offset="0.664062" stop-color="#D2D4EB"/>
<stop offset="0.671875" stop-color="#D2D5EB"/>
<stop offset="0.679687" stop-color="#D3D6EB"/>
<stop offset="0.6875" stop-color="#D4D6EC"/>
<stop offset="0.695312" stop-color="#D4D7EC"/>
<stop offset="0.703125" stop-color="#D5D8EC"/>
<stop offset="0.710938" stop-color="#D6D8ED"/>
<stop offset="0.71875" stop-color="#D7D9ED"/>
<stop offset="0.726562" stop-color="#D7DAED"/>
<stop offset="0.734375" stop-color="#D8DAEE"/>
<stop offset="0.742188" stop-color="#D9DBEE"/>
<stop offset="0.75" stop-color="#DADCEE"/>
<stop offset="0.757812" stop-color="#DADCEF"/>
<stop offset="0.765625" stop-color="#DBDDEF"/>
<stop offset="0.78125" stop-color="#DBDDEF"/>
<stop offset="0.8125" stop-color="#DBDDEF"/>
<stop offset="0.875" stop-color="#DBDDEF"/>
<stop offset="1" stop-color="#DBDDEF"/>
</linearGradient>
<linearGradient id="paint12_linear_2476_449" x1="279.417" y1="498.101" x2="293.778" y2="481.725" gradientUnits="userSpaceOnUse">
<stop stop-color="#EBA262"/>
<stop offset="0.25" stop-color="#EBA262"/>
<stop offset="0.375" stop-color="#EBA262"/>
<stop offset="0.382812" stop-color="#EBA363"/>
<stop offset="0.390625" stop-color="#ECA363"/>
<stop offset="0.398438" stop-color="#ECA464"/>
<stop offset="0.40625" stop-color="#ECA465"/>
<stop offset="0.414062" stop-color="#EDA566"/>
<stop offset="0.421875" stop-color="#EDA566"/>
<stop offset="0.429687" stop-color="#EDA667"/>
<stop offset="0.4375" stop-color="#EDA668"/>
<stop offset="0.445312" stop-color="#EEA768"/>
<stop offset="0.445313" stop-color="#EEA769"/>
<stop offset="0.453125" stop-color="#EEA869"/>
<stop offset="0.460937" stop-color="#EEA86A"/>
<stop offset="0.46875" stop-color="#EFA96B"/>
<stop offset="0.476562" stop-color="#EFA96B"/>
<stop offset="0.484375" stop-color="#EFAA6C"/>
<stop offset="0.492188" stop-color="#EFAA6D"/>
<stop offset="0.5" stop-color="#F0AB6D"/>
<stop offset="0.507812" stop-color="#F0AB6E"/>
<stop offset="0.515625" stop-color="#F0AC6F"/>
<stop offset="0.523438" stop-color="#F1AC70"/>
<stop offset="0.53125" stop-color="#F1AD70"/>
<stop offset="0.539062" stop-color="#F1AD71"/>
<stop offset="0.546875" stop-color="#F1AE72"/>
<stop offset="0.554687" stop-color="#F2AE72"/>
<stop offset="0.554688" stop-color="#F2AF73"/>
<stop offset="0.5625" stop-color="#F2AF73"/>
<stop offset="0.570312" stop-color="#F2B074"/>
<stop offset="0.578125" stop-color="#F3B075"/>
<stop offset="0.585937" stop-color="#F3B175"/>
<stop offset="0.59375" stop-color="#F3B176"/>
<stop offset="0.601562" stop-color="#F3B277"/>
<stop offset="0.609375" stop-color="#F4B277"/>
<stop offset="0.617187" stop-color="#F4B378"/>
<stop offset="0.625" stop-color="#F4B379"/>
<stop offset="0.632812" stop-color="#F5B47A"/>
<stop offset="0.640625" stop-color="#F5B47A"/>
<stop offset="0.648437" stop-color="#F5B57B"/>
<stop offset="0.65625" stop-color="#F5B67C"/>
<stop offset="0.664062" stop-color="#F6B67C"/>
<stop offset="0.671875" stop-color="#F6B77D"/>
<stop offset="0.679687" stop-color="#F6B77E"/>
<stop offset="0.6875" stop-color="#F7B87F"/>
<stop offset="0.695312" stop-color="#F7B87F"/>
<stop offset="0.703125" stop-color="#F7B980"/>
<stop offset="0.710937" stop-color="#F7B981"/>
<stop offset="0.71875" stop-color="#F8BA81"/>
<stop offset="0.726562" stop-color="#F8BA82"/>
<stop offset="0.734375" stop-color="#F8BB83"/>
<stop offset="0.742187" stop-color="#F9BC84"/>
<stop offset="0.75" stop-color="#F9BC84"/>
<stop offset="0.757812" stop-color="#F9BD85"/>
<stop offset="0.765625" stop-color="#F9BD86"/>
<stop offset="0.773437" stop-color="#FABE86"/>
<stop offset="0.78125" stop-color="#FABE87"/>
<stop offset="0.789062" stop-color="#FABF88"/>
<stop offset="0.796875" stop-color="#FBBF88"/>
<stop offset="0.804687" stop-color="#FBC089"/>
<stop offset="0.8125" stop-color="#FBC08A"/>
<stop offset="0.820312" stop-color="#FBC18B"/>
<stop offset="0.828125" stop-color="#FCC28B"/>
<stop offset="0.84375" stop-color="#FCC28C"/>
<stop offset="0.875" stop-color="#FCC28C"/>
<stop offset="1" stop-color="#FCC28C"/>
</linearGradient>
<linearGradient id="paint13_linear_2476_449" x1="285.072" y1="393.177" x2="268.676" y2="423.95" gradientUnits="userSpaceOnUse">
<stop stop-color="#FAA85D"/>
<stop offset="0.338675" stop-color="#FAA85D"/>
<stop offset="0.5" stop-color="#FAA85D"/>
<stop offset="0.5625" stop-color="#FAA85D"/>
<stop offset="0.59375" stop-color="#FAA85D"/>
<stop offset="0.597656" stop-color="#FAAA61"/>
<stop offset="0.601562" stop-color="#FAAC64"/>
<stop offset="0.605469" stop-color="#FBAE67"/>
<stop offset="0.609375" stop-color="#FBAF6B"/>
<stop offset="0.613281" stop-color="#FBB16E"/>
<stop offset="0.617188" stop-color="#FBB371"/>
<stop offset="0.621094" stop-color="#FBB574"/>
<stop offset="0.625" stop-color="#FBB778"/>
<stop offset="0.628906" stop-color="#FCB87B"/>
<stop offset="0.632812" stop-color="#FCBA7E"/>
<stop offset="0.636719" stop-color="#FCBC81"/>
<stop offset="0.640625" stop-color="#FCBE85"/>
<stop offset="0.644531" stop-color="#FCC088"/>
<stop offset="0.648438" stop-color="#FDC18B"/>
<stop offset="0.652344" stop-color="#FDC38E"/>
<stop offset="0.65625" stop-color="#FDC490"/>
<stop offset="0.661325" stop-color="#FDC490"/>
<stop offset="0.6875" stop-color="#FDC490"/>
<stop offset="0.75" stop-color="#FDC490"/>
<stop offset="1" stop-color="#FDC490"/>
</linearGradient>
<linearGradient id="paint14_linear_2476_449" x1="265.071" y1="416.182" x2="289.897" y2="368.366" gradientUnits="userSpaceOnUse">
<stop stop-color="#FCBC80"/>
<stop offset="0.25" stop-color="#FCBC80"/>
<stop offset="0.28125" stop-color="#FCBC80"/>
<stop offset="0.296875" stop-color="#FCBC80"/>
<stop offset="0.300781" stop-color="#FCBC81"/>
<stop offset="0.304688" stop-color="#FCBD81"/>
<stop offset="0.308594" stop-color="#FCBD82"/>
<stop offset="0.3125" stop-color="#FCBD83"/>
<stop offset="0.316406" stop-color="#FCBE83"/>
<stop offset="0.320312" stop-color="#FCBE84"/>
<stop offset="0.324219" stop-color="#FCBE85"/>
<stop offset="0.328125" stop-color="#FCBF85"/>
<stop offset="0.332031" stop-color="#FCBF86"/>
<stop offset="0.335938" stop-color="#FCBF87"/>
<stop offset="0.339844" stop-color="#FCC087"/>
<stop offset="0.34375" stop-color="#FCC088"/>
<stop offset="0.347656" stop-color="#FCC089"/>
<stop offset="0.351563" stop-color="#FCC189"/>
<stop offset="0.355469" stop-color="#FCC18A"/>
<stop offset="0.359375" stop-color="#FCC28B"/>
<stop offset="0.363281" stop-color="#FCC28B"/>
<stop offset="0.367188" stop-color="#FCC28C"/>
<stop offset="0.371094" stop-color="#FCC38D"/>
<stop offset="0.375" stop-color="#FCC38D"/>
<stop offset="0.378906" stop-color="#FCC38E"/>
<stop offset="0.382813" stop-color="#FDC48F"/>
<stop offset="0.386719" stop-color="#FDC48F"/>
<stop offset="0.390625" stop-color="#FDC490"/>
<stop offset="0.394531" stop-color="#FDC590"/>
<stop offset="0.398438" stop-color="#FDC591"/>
<stop offset="0.402344" stop-color="#FDC592"/>
<stop offset="0.40625" stop-color="#FDC692"/>
<stop offset="0.410156" stop-color="#FDC693"/>
<stop offset="0.414063" stop-color="#FDC694"/>
<stop offset="0.417969" stop-color="#FDC794"/>
<stop offset="0.421875" stop-color="#FDC795"/>
<stop offset="0.425781" stop-color="#FDC796"/>
<stop offset="0.429688" stop-color="#FDC896"/>
<stop offset="0.433594" stop-color="#FDC897"/>
<stop offset="0.4375" stop-color="#FDC898"/>
<stop offset="0.441406" stop-color="#FDC998"/>
<stop offset="0.445312" stop-color="#FDC999"/>
<stop offset="0.449219" stop-color="#FDCA9A"/>
<stop offset="0.453125" stop-color="#FDCA9A"/>
<stop offset="0.457031" stop-color="#FDCA9B"/>
<stop offset="0.460938" stop-color="#FDCB9C"/>
<stop offset="0.464844" stop-color="#FDCB9C"/>
<stop offset="0.46875" stop-color="#FDCB9D"/>
<stop offset="0.472656" stop-color="#FDCC9E"/>
<stop offset="0.476562" stop-color="#FDCC9E"/>
<stop offset="0.480469" stop-color="#FDCC9F"/>
<stop offset="0.484375" stop-color="#FDCDA0"/>
<stop offset="0.488281" stop-color="#FDCDA0"/>
<stop offset="0.492188" stop-color="#FDCDA1"/>
<stop offset="0.496094" stop-color="#FDCEA2"/>
<stop offset="0.5" stop-color="#FDCEA2"/>
<stop offset="0.503906" stop-color="#FDCEA3"/>
<stop offset="0.507812" stop-color="#FDCFA3"/>
<stop offset="0.511719" stop-color="#FDCFA4"/>
<stop offset="0.515625" stop-color="#FDCFA5"/>
<stop offset="0.519531" stop-color="#FDD0A5"/>
<stop offset="0.523438" stop-color="#FDD0A6"/>
<stop offset="0.527344" stop-color="#FDD0A7"/>
<stop offset="0.53125" stop-color="#FED1A7"/>
<stop offset="0.535156" stop-color="#FED1A8"/>
<stop offset="0.539063" stop-color="#FED2A9"/>
<stop offset="0.542969" stop-color="#FED2A9"/>
<stop offset="0.546875" stop-color="#FED2AA"/>
<stop offset="0.550781" stop-color="#FED3AB"/>
<stop offset="0.554688" stop-color="#FED3AB"/>
<stop offset="0.558594" stop-color="#FED3AC"/>
<stop offset="0.5625" stop-color="#FED4AD"/>
<stop offset="0.566406" stop-color="#FED4AD"/>
<stop offset="0.570313" stop-color="#FED4AE"/>
<stop offset="0.574219" stop-color="#FED5AF"/>
<stop offset="0.578125" stop-color="#FED5AF"/>
<stop offset="0.582031" stop-color="#FED5B0"/>
<stop offset="0.585938" stop-color="#FED6B1"/>
<stop offset="0.589844" stop-color="#FED6B1"/>
<stop offset="0.59375" stop-color="#FED6B2"/>
<stop offset="0.597656" stop-color="#FED7B2"/>
<stop offset="0.601562" stop-color="#FED7B3"/>
<stop offset="0.609375" stop-color="#FED7B3"/>
<stop offset="0.625" stop-color="#FED7B3"/>
<stop offset="0.672958" stop-color="#FED7B3"/>
<stop offset="0.75" stop-color="#FED7B3"/>
<stop offset="1" stop-color="#FED7B3"/>
</linearGradient>
<linearGradient id="paint15_linear_2476_449" x1="251.164" y1="397.628" x2="308.641" y2="363.924" gradientUnits="userSpaceOnUse">
<stop stop-color="#201D3E"/>
<stop offset="0.25" stop-color="#201D3E"/>
<stop offset="0.335766" stop-color="#201D3E"/>
<stop offset="0.375" stop-color="#201D3E"/>
<stop offset="0.4375" stop-color="#201D3E"/>
<stop offset="0.441406" stop-color="#201E3F"/>
<stop offset="0.445312" stop-color="#211E40"/>
<stop offset="0.449219" stop-color="#211E41"/>
<stop offset="0.453125" stop-color="#211F41"/>
<stop offset="0.457031" stop-color="#221F42"/>
<stop offset="0.460938" stop-color="#221F43"/>
<stop offset="0.464844" stop-color="#222043"/>
<stop offset="0.46875" stop-color="#222044"/>
<stop offset="0.472656" stop-color="#232045"/>
<stop offset="0.476562" stop-color="#232146"/>
<stop offset="0.480469" stop-color="#232146"/>
<stop offset="0.484375" stop-color="#232147"/>
<stop offset="0.488281" stop-color="#242248"/>
<stop offset="0.492188" stop-color="#242248"/>
<stop offset="0.496094" stop-color="#242249"/>
<stop offset="0.5" stop-color="#25234A"/>
<stop offset="0.503906" stop-color="#25234A"/>
<stop offset="0.507812" stop-color="#25234B"/>
<stop offset="0.511719" stop-color="#25244C"/>
<stop offset="0.515625" stop-color="#26244C"/>
<stop offset="0.519531" stop-color="#26244D"/>
<stop offset="0.523438" stop-color="#26254E"/>
<stop offset="0.527344" stop-color="#26254E"/>
<stop offset="0.53125" stop-color="#27254F"/>
<stop offset="0.535156" stop-color="#272650"/>
<stop offset="0.539062" stop-color="#272651"/>
<stop offset="0.542969" stop-color="#282651"/>
<stop offset="0.546875" stop-color="#282752"/>
<stop offset="0.550781" stop-color="#282753"/>
<stop offset="0.554688" stop-color="#282753"/>
<stop offset="0.558594" stop-color="#292854"/>
<stop offset="0.5625" stop-color="#292855"/>
<stop offset="0.566406" stop-color="#292855"/>
<stop offset="0.570312" stop-color="#2A2956"/>
<stop offset="0.574219" stop-color="#2A2957"/>
<stop offset="0.578125" stop-color="#2A2957"/>
<stop offset="0.582031" stop-color="#2A2A58"/>
<stop offset="0.585938" stop-color="#2B2A59"/>
<stop offset="0.589844" stop-color="#2B2A5A"/>
<stop offset="0.59375" stop-color="#2B2B5A"/>
<stop offset="0.597656" stop-color="#2B2B5B"/>
<stop offset="0.601562" stop-color="#2C2B5C"/>
<stop offset="0.605469" stop-color="#2C2C5C"/>
<stop offset="0.609375" stop-color="#2C2C5D"/>
<stop offset="0.613281" stop-color="#2D2C5E"/>
<stop offset="0.617188" stop-color="#2D2D5E"/>
<stop offset="0.621094" stop-color="#2D2D5F"/>
<stop offset="0.625" stop-color="#2D2D60"/>
<stop offset="0.628906" stop-color="#2E2E60"/>
<stop offset="0.632812" stop-color="#2E2E61"/>
<stop offset="0.636719" stop-color="#2E2E62"/>
<stop offset="0.640625" stop-color="#2E2F63"/>
<stop offset="0.644531" stop-color="#2F2F63"/>
<stop offset="0.648438" stop-color="#2F2F64"/>
<stop offset="0.652344" stop-color="#2F3065"/>
<stop offset="0.65625" stop-color="#303065"/>
<stop offset="0.660156" stop-color="#303066"/>
<stop offset="0.664062" stop-color="#303167"/>
<stop offset="0.667969" stop-color="#303167"/>
<stop offset="0.671875" stop-color="#313168"/>
<stop offset="0.675781" stop-color="#313269"/>
<stop offset="0.679688" stop-color="#313269"/>
<stop offset="0.683594" stop-color="#32326A"/>
<stop offset="0.6875" stop-color="#32336B"/>
<stop offset="0.691406" stop-color="#32336C"/>
<stop offset="0.695312" stop-color="#32336C"/>
<stop offset="0.699219" stop-color="#33346D"/>
<stop offset="0.703125" stop-color="#33346E"/>
<stop offset="0.71875" stop-color="#33346E"/>
<stop offset="0.75" stop-color="#33346E"/>
<stop offset="1" stop-color="#33346E"/>
</linearGradient>
<linearGradient id="paint16_linear_2476_449" x1="625.484" y1="467.403" x2="623.675" y2="436.586" gradientUnits="userSpaceOnUse">
<stop stop-color="#FBB677"/>
<stop offset="0.116344" stop-color="#FBB677"/>
<stop offset="0.25" stop-color="#FBB677"/>
<stop offset="0.375" stop-color="#FBB677"/>
<stop offset="0.4375" stop-color="#FBB677"/>
<stop offset="0.453125" stop-color="#FBB677"/>
<stop offset="0.457031" stop-color="#FBB778"/>
<stop offset="0.460938" stop-color="#FBB779"/>
<stop offset="0.464844" stop-color="#FBB77A"/>
<stop offset="0.46875" stop-color="#FBB87A"/>
<stop offset="0.472656" stop-color="#FBB87B"/>
<stop offset="0.476562" stop-color="#FBB97B"/>
<stop offset="0.480469" stop-color="#FBB97C"/>
<stop offset="0.484375" stop-color="#FBB97D"/>
<stop offset="0.488281" stop-color="#FBBA7D"/>
<stop offset="0.492188" stop-color="#FBBA7E"/>
<stop offset="0.496094" stop-color="#FBBA7E"/>
<stop offset="0.5" stop-color="#FBBB7F"/>
<stop offset="0.503906" stop-color="#FBBB80"/>
<stop offset="0.507813" stop-color="#FBBB80"/>
<stop offset="0.511719" stop-color="#FBBC81"/>
<stop offset="0.515625" stop-color="#FBBC81"/>
<stop offset="0.519531" stop-color="#FBBC82"/>
<stop offset="0.523438" stop-color="#FBBD83"/>
<stop offset="0.527344" stop-color="#FBBD83"/>
<stop offset="0.53125" stop-color="#FBBD84"/>
<stop offset="0.535156" stop-color="#FCBE84"/>
<stop offset="0.539062" stop-color="#FCBE85"/>
<stop offset="0.542969" stop-color="#FCBF86"/>
<stop offset="0.546875" stop-color="#FCBF86"/>
<stop offset="0.550781" stop-color="#FCBF87"/>
<stop offset="0.554688" stop-color="#FCC087"/>
<stop offset="0.558594" stop-color="#FCC088"/>
<stop offset="0.5625" stop-color="#FCC089"/>
<stop offset="0.566406" stop-color="#FCC189"/>
<stop offset="0.570313" stop-color="#FCC18A"/>
<stop offset="0.574219" stop-color="#FCC18A"/>
<stop offset="0.578125" stop-color="#FCC28B"/>
<stop offset="0.582031" stop-color="#FCC28C"/>
<stop offset="0.585938" stop-color="#FCC28C"/>
<stop offset="0.589844" stop-color="#FCC38D"/>
<stop offset="0.59375" stop-color="#FCC38D"/>
<stop offset="0.597656" stop-color="#FCC38E"/>
<stop offset="0.601562" stop-color="#FCC48F"/>
<stop offset="0.605469" stop-color="#FCC48F"/>
<stop offset="0.609375" stop-color="#FCC490"/>
<stop offset="0.613281" stop-color="#FCC590"/>
<stop offset="0.617188" stop-color="#FCC591"/>
<stop offset="0.621094" stop-color="#FCC692"/>
<stop offset="0.625" stop-color="#FCC692"/>
<stop offset="0.628906" stop-color="#FCC693"/>
<stop offset="0.632813" stop-color="#FCC793"/>
<stop offset="0.636719" stop-color="#FCC794"/>
<stop offset="0.640625" stop-color="#FCC795"/>
<stop offset="0.644531" stop-color="#FCC895"/>
<stop offset="0.648438" stop-color="#FCC896"/>
<stop offset="0.652344" stop-color="#FCC896"/>
<stop offset="0.65625" stop-color="#FCC997"/>
<stop offset="0.660156" stop-color="#FCC998"/>
<stop offset="0.664063" stop-color="#FCC998"/>
<stop offset="0.667969" stop-color="#FCCA99"/>
<stop offset="0.671875" stop-color="#FCCA99"/>
<stop offset="0.675781" stop-color="#FDCA9A"/>
<stop offset="0.679688" stop-color="#FDCB9B"/>
<stop offset="0.683594" stop-color="#FDCB9B"/>
<stop offset="0.6875" stop-color="#FDCC9C"/>
<stop offset="0.691406" stop-color="#FDCC9C"/>
<stop offset="0.695312" stop-color="#FDCC9D"/>
<stop offset="0.699219" stop-color="#FDCD9E"/>
<stop offset="0.703125" stop-color="#FDCD9E"/>
<stop offset="0.707031" stop-color="#FDCD9F"/>
<stop offset="0.710938" stop-color="#FDCE9F"/>
<stop offset="0.714844" stop-color="#FDCEA0"/>
<stop offset="0.71875" stop-color="#FDCEA1"/>
<stop offset="0.722656" stop-color="#FDCFA1"/>
<stop offset="0.726563" stop-color="#FDCFA2"/>
<stop offset="0.730469" stop-color="#FDCFA2"/>
<stop offset="0.734375" stop-color="#FDD0A3"/>
<stop offset="0.738281" stop-color="#FDD0A4"/>
<stop offset="0.742188" stop-color="#FDD0A4"/>
<stop offset="0.746094" stop-color="#FDD1A5"/>
<stop offset="0.75" stop-color="#FDD1A5"/>
<stop offset="0.753906" stop-color="#FDD1A6"/>
<stop offset="0.757813" stop-color="#FDD2A7"/>
<stop offset="0.761719" stop-color="#FDD2A7"/>
<stop offset="0.765625" stop-color="#FDD3A8"/>
<stop offset="0.769531" stop-color="#FDD3A8"/>
<stop offset="0.773438" stop-color="#FDD3A9"/>
<stop offset="0.777344" stop-color="#FDD4AA"/>
<stop offset="0.78125" stop-color="#FDD4AA"/>
<stop offset="0.785156" stop-color="#FDD4AB"/>
<stop offset="0.789063" stop-color="#FDD5AB"/>
<stop offset="0.792969" stop-color="#FDD5AC"/>
<stop offset="0.796875" stop-color="#FDD5AD"/>
<stop offset="0.800781" stop-color="#FDD6AD"/>
<stop offset="0.804688" stop-color="#FDD6AE"/>
<stop offset="0.808594" stop-color="#FDD6AE"/>
<stop offset="0.8125" stop-color="#FDD7AF"/>
<stop offset="0.816406" stop-color="#FED7B0"/>
<stop offset="0.820313" stop-color="#FED7B0"/>
<stop offset="0.824219" stop-color="#FED8B1"/>
<stop offset="0.828125" stop-color="#FED8B1"/>
<stop offset="0.832031" stop-color="#FED9B2"/>
<stop offset="0.835938" stop-color="#FED9B3"/>
<stop offset="0.839844" stop-color="#FED9B3"/>
<stop offset="0.84375" stop-color="#FEDAB4"/>
<stop offset="0.847656" stop-color="#FEDAB4"/>
<stop offset="0.851563" stop-color="#FEDAB5"/>
<stop offset="0.855469" stop-color="#FEDBB6"/>
<stop offset="0.859375" stop-color="#FEDBB6"/>
<stop offset="0.863281" stop-color="#FEDBB7"/>
<stop offset="0.867188" stop-color="#FEDCB8"/>
<stop offset="0.871094" stop-color="#FEDCB8"/>
<stop offset="0.875" stop-color="#FEDCB9"/>
<stop offset="0.878906" stop-color="#FEDDB9"/>
<stop offset="0.882812" stop-color="#FEDDBA"/>
<stop offset="0.883656" stop-color="#FEDDBA"/>
<stop offset="0.890625" stop-color="#FEDDBA"/>
<stop offset="0.90625" stop-color="#FEDDBA"/>
<stop offset="0.9375" stop-color="#FEDDBA"/>
<stop offset="1" stop-color="#FEDDBA"/>
</linearGradient>
<linearGradient id="paint17_linear_2476_449" x1="666.996" y1="454.293" x2="626.639" y2="413.239" gradientUnits="userSpaceOnUse">
<stop stop-color="#ED1C24"/>
<stop offset="0.25" stop-color="#ED1C24"/>
<stop offset="0.28125" stop-color="#ED1C24"/>
<stop offset="0.289062" stop-color="#ED1C24"/>
<stop offset="0.292969" stop-color="#ED1D26"/>
<stop offset="0.296875" stop-color="#ED1E27"/>
<stop offset="0.300781" stop-color="#ED2029"/>
<stop offset="0.304688" stop-color="#ED212B"/>
<stop offset="0.308594" stop-color="#ED222C"/>
<stop offset="0.3125" stop-color="#ED232E"/>
<stop offset="0.316406" stop-color="#ED2430"/>
<stop offset="0.320312" stop-color="#ED2632"/>
<stop offset="0.324219" stop-color="#EE2733"/>
<stop offset="0.328125" stop-color="#EE2835"/>
<stop offset="0.332031" stop-color="#EE2937"/>
<stop offset="0.335938" stop-color="#EE2A38"/>
<stop offset="0.339844" stop-color="#EE2C3A"/>
<stop offset="0.34375" stop-color="#EE2D3C"/>
<stop offset="0.347656" stop-color="#EE2E3D"/>
<stop offset="0.351563" stop-color="#EE2F3F"/>
<stop offset="0.355469" stop-color="#EE3041"/>
<stop offset="0.359375" stop-color="#EE3143"/>
<stop offset="0.363281" stop-color="#EE3344"/>
<stop offset="0.367188" stop-color="#EE3446"/>
<stop offset="0.371094" stop-color="#EE3548"/>
<stop offset="0.375" stop-color="#EF3649"/>
<stop offset="0.378906" stop-color="#EF374B"/>
<stop offset="0.382813" stop-color="#EF394D"/>
<stop offset="0.386719" stop-color="#EF3A4E"/>
<stop offset="0.390625" stop-color="#EF3B50"/>
<stop offset="0.394531" stop-color="#EF3C52"/>
<stop offset="0.398438" stop-color="#EF3D53"/>
<stop offset="0.402344" stop-color="#EF3E54"/>
<stop offset="0.40625" stop-color="#EF3F55"/>
<stop offset="0.410156" stop-color="#EF4056"/>
<stop offset="0.414062" stop-color="#EF4157"/>
<stop offset="0.417969" stop-color="#EF4258"/>
<stop offset="0.421875" stop-color="#EF4359"/>
<stop offset="0.425781" stop-color="#EF4359"/>
<stop offset="0.429688" stop-color="#EF445A"/>
<stop offset="0.433594" stop-color="#EF455B"/>
<stop offset="0.4375" stop-color="#EF465C"/>
<stop offset="0.441406" stop-color="#EF475D"/>
<stop offset="0.445312" stop-color="#EF485E"/>
<stop offset="0.449219" stop-color="#EF495F"/>
<stop offset="0.453125" stop-color="#EF4960"/>
<stop offset="0.457031" stop-color="#EF4A60"/>
<stop offset="0.460938" stop-color="#F04B61"/>
<stop offset="0.464844" stop-color="#F04C62"/>
<stop offset="0.46875" stop-color="#F04D63"/>
<stop offset="0.472656" stop-color="#F04E64"/>
<stop offset="0.476562" stop-color="#F04F65"/>
<stop offset="0.480469" stop-color="#F05066"/>
<stop offset="0.484375" stop-color="#F05067"/>
<stop offset="0.488281" stop-color="#F05167"/>
<stop offset="0.492188" stop-color="#F05268"/>
<stop offset="0.496094" stop-color="#F05369"/>
<stop offset="0.5" stop-color="#F0546A"/>
<stop offset="0.503906" stop-color="#F0556B"/>
<stop offset="0.507812" stop-color="#F0566C"/>
<stop offset="0.515625" stop-color="#F0566C"/>
<stop offset="0.53125" stop-color="#F0566C"/>
<stop offset="0.5625" stop-color="#F0566C"/>
<stop offset="0.625" stop-color="#F0566C"/>
<stop offset="0.628106" stop-color="#F0566C"/>
<stop offset="0.75" stop-color="#F0566C"/>
<stop offset="1" stop-color="#F0566C"/>
</linearGradient>
<linearGradient id="paint18_linear_2476_449" x1="623.349" y1="456.976" x2="623.394" y2="455.741" gradientUnits="userSpaceOnUse">
<stop stop-color="#6766A7"/>
<stop offset="0.00390625" stop-color="#6766A6"/>
<stop offset="0.0078125" stop-color="#6765A6"/>
<stop offset="0.0117188" stop-color="#6665A5"/>
<stop offset="0.015625" stop-color="#6665A4"/>
<stop offset="0.0195312" stop-color="#6564A4"/>
<stop offset="0.0234375" stop-color="#6564A3"/>
<stop offset="0.0273438" stop-color="#6463A2"/>
<stop offset="0.03125" stop-color="#6463A2"/>
<stop offset="0.0351562" stop-color="#6462A1"/>
<stop offset="0.0390625" stop-color="#6362A1"/>
<stop offset="0.0429688" stop-color="#6361A0"/>
<stop offset="0.046875" stop-color="#62619F"/>
<stop offset="0.0507812" stop-color="#62609F"/>
<stop offset="0.0546875" stop-color="#61609E"/>
<stop offset="0.0585937" stop-color="#615F9E"/>
<stop offset="0.0625" stop-color="#615F9D"/>
<stop offset="0.0664062" stop-color="#605F9C"/>
<stop offset="0.0703125" stop-color="#605E9C"/>
<stop offset="0.0742187" stop-color="#5F5E9B"/>
<stop offset="0.078125" stop-color="#5F5D9A"/>
<stop offset="0.0820312" stop-color="#5E5D9A"/>
<stop offset="0.0859375" stop-color="#5E5C99"/>
<stop offset="0.0898437" stop-color="#5D5C99"/>
<stop offset="0.09375" stop-color="#5D5B98"/>
<stop offset="0.0976562" stop-color="#5D5B97"/>
<stop offset="0.101562" stop-color="#5C5A97"/>
<stop offset="0.105469" stop-color="#5C5A96"/>
<stop offset="0.109375" stop-color="#5B5A95"/>
<stop offset="0.113281" stop-color="#5B5995"/>
<stop offset="0.117188" stop-color="#5A5994"/>
<stop offset="0.121094" stop-color="#5A5894"/>
<stop offset="0.125" stop-color="#5A5893"/>
<stop offset="0.128906" stop-color="#595792"/>
<stop offset="0.132812" stop-color="#595792"/>
<stop offset="0.136719" stop-color="#585691"/>
<stop offset="0.140625" stop-color="#585691"/>
<stop offset="0.144531" stop-color="#575590"/>
<stop offset="0.148438" stop-color="#57558F"/>
<stop offset="0.152344" stop-color="#57548F"/>
<stop offset="0.15625" stop-color="#56548E"/>
<stop offset="0.160156" stop-color="#56548D"/>
<stop offset="0.164062" stop-color="#55538D"/>
<stop offset="0.167969" stop-color="#55538C"/>
<stop offset="0.171875" stop-color="#54528C"/>
<stop offset="0.175781" stop-color="#54528B"/>
<stop offset="0.179687" stop-color="#53518A"/>
<stop offset="0.183594" stop-color="#53518A"/>
<stop offset="0.1875" stop-color="#535089"/>
<stop offset="0.191406" stop-color="#525088"/>
<stop offset="0.195312" stop-color="#524F88"/>
<stop offset="0.199219" stop-color="#514F87"/>
<stop offset="0.203125" stop-color="#514F87"/>
<stop offset="0.207031" stop-color="#504E86"/>
<stop offset="0.210938" stop-color="#504E85"/>
<stop offset="0.214844" stop-color="#504D85"/>
<stop offset="0.21875" stop-color="#4F4D84"/>
<stop offset="0.222656" stop-color="#4F4C84"/>
<stop offset="0.226563" stop-color="#4E4C83"/>
<stop offset="0.230469" stop-color="#4E4B82"/>
<stop offset="0.234375" stop-color="#4D4B82"/>
<stop offset="0.238281" stop-color="#4D4A81"/>
<stop offset="0.242188" stop-color="#4D4A80"/>
<stop offset="0.246094" stop-color="#4C4980"/>
<stop offset="0.25" stop-color="#4C497F"/>
<stop offset="0.253906" stop-color="#4B497F"/>
<stop offset="0.257812" stop-color="#4B487E"/>
<stop offset="0.261719" stop-color="#4A487D"/>
<stop offset="0.265625" stop-color="#4A477D"/>
<stop offset="0.269531" stop-color="#49477C"/>
<stop offset="0.273438" stop-color="#49467B"/>
<stop offset="0.277344" stop-color="#49467B"/>
<stop offset="0.28125" stop-color="#48457A"/>
<stop offset="0.285156" stop-color="#48457A"/>
<stop offset="0.289062" stop-color="#474479"/>
<stop offset="0.292969" stop-color="#474478"/>
<stop offset="0.296875" stop-color="#464378"/>
<stop offset="0.300781" stop-color="#464377"/>
<stop offset="0.304688" stop-color="#464377"/>
<stop offset="0.308594" stop-color="#454276"/>
<stop offset="0.3125" stop-color="#454275"/>
<stop offset="0.316406" stop-color="#444175"/>
<stop offset="0.320312" stop-color="#444174"/>
<stop offset="0.324219" stop-color="#434073"/>
<stop offset="0.328125" stop-color="#434073"/>
<stop offset="0.332031" stop-color="#433F72"/>
<stop offset="0.335938" stop-color="#423F72"/>
<stop offset="0.339844" stop-color="#423E71"/>
<stop offset="0.34375" stop-color="#413E70"/>
<stop offset="0.347656" stop-color="#413E70"/>
<stop offset="0.351562" stop-color="#403D6F"/>
<stop offset="0.355469" stop-color="#403D6E"/>
<stop offset="0.359375" stop-color="#3F3C6E"/>
<stop offset="0.363281" stop-color="#3F3C6D"/>
<stop offset="0.367187" stop-color="#3F3B6D"/>
<stop offset="0.371094" stop-color="#3E3B6C"/>
<stop offset="0.375" stop-color="#3E3A6B"/>
<stop offset="0.378906" stop-color="#3D3A6B"/>
<stop offset="0.382812" stop-color="#3D396A"/>
<stop offset="0.386719" stop-color="#3C3969"/>
<stop offset="0.390625" stop-color="#3C3869"/>
<stop offset="0.394531" stop-color="#3C3868"/>
<stop offset="0.398438" stop-color="#3B3868"/>
<stop offset="0.402344" stop-color="#3B3767"/>
<stop offset="0.40625" stop-color="#3A3766"/>
<stop offset="0.410156" stop-color="#3A3666"/>
<stop offset="0.414062" stop-color="#393665"/>
<stop offset="0.417969" stop-color="#393565"/>
<stop offset="0.421875" stop-color="#383564"/>
<stop offset="0.425781" stop-color="#383463"/>
<stop offset="0.429688" stop-color="#383463"/>
<stop offset="0.433594" stop-color="#373362"/>
<stop offset="0.4375" stop-color="#373361"/>
<stop offset="0.441406" stop-color="#363261"/>
<stop offset="0.445313" stop-color="#363260"/>
<stop offset="0.449219" stop-color="#353260"/>
<stop offset="0.453125" stop-color="#35315F"/>
<stop offset="0.457031" stop-color="#35315E"/>
<stop offset="0.460938" stop-color="#34305E"/>
<stop offset="0.464844" stop-color="#34305D"/>
<stop offset="0.46875" stop-color="#332F5C"/>
<stop offset="0.472656" stop-color="#332F5C"/>
<stop offset="0.476563" stop-color="#322E5B"/>
<stop offset="0.480469" stop-color="#322E5B"/>
<stop offset="0.484375" stop-color="#322D5A"/>
<stop offset="0.488281" stop-color="#312D59"/>
<stop offset="0.492188" stop-color="#312D59"/>
<stop offset="0.496094" stop-color="#302C58"/>
<stop offset="0.5" stop-color="#302C58"/>
<stop offset="0.503906" stop-color="#2F2B57"/>
<stop offset="0.507812" stop-color="#2F2B56"/>
<stop offset="0.511719" stop-color="#2E2A56"/>
<stop offset="0.515625" stop-color="#2E2A55"/>
<stop offset="0.519531" stop-color="#2E2954"/>
<stop offset="0.523437" stop-color="#2D2954"/>
<stop offset="0.527344" stop-color="#2D2853"/>
<stop offset="0.53125" stop-color="#2C2853"/>
<stop offset="0.535156" stop-color="#2C2752"/>
<stop offset="0.539062" stop-color="#2B2751"/>
<stop offset="0.542969" stop-color="#2B2751"/>
<stop offset="0.546875" stop-color="#2B2650"/>
<stop offset="0.550781" stop-color="#2A264F"/>
<stop offset="0.554688" stop-color="#2A254F"/>
<stop offset="0.558594" stop-color="#29254E"/>
<stop offset="0.5625" stop-color="#29244E"/>
<stop offset="0.566406" stop-color="#28244D"/>
<stop offset="0.570312" stop-color="#28234C"/>
<stop offset="0.574219" stop-color="#28234C"/>
<stop offset="0.578125" stop-color="#27224B"/>
<stop offset="0.582031" stop-color="#27224B"/>
<stop offset="0.585938" stop-color="#26214A"/>
<stop offset="0.589844" stop-color="#262149"/>
<stop offset="0.59375" stop-color="#252149"/>
<stop offset="0.597656" stop-color="#252048"/>
<stop offset="0.601562" stop-color="#242047"/>
<stop offset="0.605469" stop-color="#241F47"/>
<stop offset="0.609375" stop-color="#241F46"/>
<stop offset="0.613281" stop-color="#231E46"/>
<stop offset="0.617188" stop-color="#231E45"/>
<stop offset="0.621094" stop-color="#221D44"/>
<stop offset="0.625" stop-color="#221D44"/>
<stop offset="0.628906" stop-color="#211C43"/>
<stop offset="0.632812" stop-color="#211C42"/>
<stop offset="0.636719" stop-color="#211C42"/>
<stop offset="0.640625" stop-color="#201B41"/>
<stop offset="0.644531" stop-color="#201B41"/>
<stop offset="0.648438" stop-color="#1F1A40"/>
<stop offset="0.652344" stop-color="#1F1A3F"/>
<stop offset="0.65625" stop-color="#1E193F"/>
<stop offset="0.660156" stop-color="#1E193E"/>
<stop offset="0.664063" stop-color="#1E183E"/>
<stop offset="0.667969" stop-color="#1D183D"/>
<stop offset="0.671875" stop-color="#1D173C"/>
<stop offset="0.675781" stop-color="#1C173C"/>
<stop offset="0.679688" stop-color="#1C163B"/>
<stop offset="0.683594" stop-color="#1B163A"/>
<stop offset="0.6875" stop-color="#1B163A"/>
<stop offset="0.691406" stop-color="#1A1539"/>
<stop offset="0.695312" stop-color="#1A1539"/>
<stop offset="0.699219" stop-color="#1A1438"/>
<stop offset="0.703125" stop-color="#191437"/>
<stop offset="0.707031" stop-color="#191337"/>
<stop offset="0.710937" stop-color="#181336"/>
<stop offset="0.714844" stop-color="#181235"/>
<stop offset="0.71875" stop-color="#171235"/>
<stop offset="0.722656" stop-color="#171134"/>
<stop offset="0.726562" stop-color="#171134"/>
<stop offset="0.730469" stop-color="#161133"/>
<stop offset="0.734375" stop-color="#161032"/>
<stop offset="0.738281" stop-color="#151032"/>
<stop offset="0.742188" stop-color="#150F31"/>
<stop offset="0.746094" stop-color="#140F30"/>
<stop offset="0.75" stop-color="#140E30"/>
<stop offset="0.753906" stop-color="#140E2F"/>
<stop offset="0.757812" stop-color="#130D2F"/>
<stop offset="0.761719" stop-color="#130D2E"/>
<stop offset="0.765625" stop-color="#120C2D"/>
<stop offset="0.769531" stop-color="#120C2D"/>
<stop offset="0.773438" stop-color="#110B2C"/>
<stop offset="0.777344" stop-color="#110B2C"/>
<stop offset="0.78125" stop-color="#100B2B"/>
<stop offset="0.785156" stop-color="#100A2A"/>
<stop offset="0.789062" stop-color="#100A2A"/>
<stop offset="0.792969" stop-color="#0F0929"/>
<stop offset="0.796875" stop-color="#0F0928"/>
<stop offset="0.800781" stop-color="#0E0828"/>
<stop offset="0.804688" stop-color="#0E0827"/>
<stop offset="0.808594" stop-color="#0D0727"/>
<stop offset="0.8125" stop-color="#0D0726"/>
<stop offset="0.875" stop-color="#0D0726"/>
<stop offset="0.879987" stop-color="#0D0726"/>
<stop offset="1" stop-color="#0D0726"/>
</linearGradient>
<linearGradient id="paint19_linear_2476_449" x1="592.909" y1="456.759" x2="592.946" y2="454.817" gradientUnits="userSpaceOnUse">
<stop stop-color="#8A8DC2"/>
<stop offset="0.0193976" stop-color="#8A8DC2"/>
<stop offset="0.03125" stop-color="#8A8DC2"/>
<stop offset="0.0390625" stop-color="#8A8DC2"/>
<stop offset="0.0429688" stop-color="#898CC1"/>
<stop offset="0.046875" stop-color="#898CC0"/>
<stop offset="0.0507812" stop-color="#888BC0"/>
<stop offset="0.0546875" stop-color="#888BBF"/>
<stop offset="0.0585938" stop-color="#878ABF"/>
<stop offset="0.0625" stop-color="#878ABE"/>
<stop offset="0.0664062" stop-color="#8689BE"/>
<stop offset="0.0703125" stop-color="#8689BD"/>
<stop offset="0.0742188" stop-color="#8688BD"/>
<stop offset="0.078125" stop-color="#8588BC"/>
<stop offset="0.0820312" stop-color="#8587BB"/>
<stop offset="0.0859375" stop-color="#8487BB"/>
<stop offset="0.0898438" stop-color="#8486BA"/>
<stop offset="0.09375" stop-color="#8386BA"/>
<stop offset="0.0976562" stop-color="#8386B9"/>
<stop offset="0.101562" stop-color="#8285B9"/>
<stop offset="0.105469" stop-color="#8285B8"/>
<stop offset="0.109375" stop-color="#8284B8"/>
<stop offset="0.113281" stop-color="#8184B7"/>
<stop offset="0.117188" stop-color="#8183B6"/>
<stop offset="0.121094" stop-color="#8083B6"/>
<stop offset="0.125" stop-color="#8082B5"/>
<stop offset="0.128906" stop-color="#7F82B5"/>
<stop offset="0.132812" stop-color="#7F81B4"/>
<stop offset="0.136719" stop-color="#7E81B4"/>
<stop offset="0.140625" stop-color="#7E80B3"/>
<stop offset="0.144531" stop-color="#7E80B3"/>
<stop offset="0.148438" stop-color="#7D7FB2"/>
<stop offset="0.152344" stop-color="#7D7FB2"/>
<stop offset="0.15625" stop-color="#7C7FB1"/>
<stop offset="0.160156" stop-color="#7C7EB0"/>
<stop offset="0.164062" stop-color="#7B7EB0"/>
<stop offset="0.167969" stop-color="#7B7DAF"/>
<stop offset="0.171875" stop-color="#7A7DAF"/>
<stop offset="0.175781" stop-color="#7A7CAE"/>
<stop offset="0.179687" stop-color="#7A7CAE"/>
<stop offset="0.183594" stop-color="#797BAD"/>
<stop offset="0.1875" stop-color="#797BAD"/>
<stop offset="0.191406" stop-color="#787AAC"/>
<stop offset="0.195312" stop-color="#787AAB"/>
<stop offset="0.199219" stop-color="#7779AB"/>
<stop offset="0.203125" stop-color="#7779AA"/>
<stop offset="0.207031" stop-color="#7678AA"/>
<stop offset="0.210937" stop-color="#7678A9"/>
<stop offset="0.214844" stop-color="#7678A9"/>
<stop offset="0.21875" stop-color="#7577A8"/>
<stop offset="0.222656" stop-color="#7577A8"/>
<stop offset="0.226562" stop-color="#7476A7"/>
<stop offset="0.230469" stop-color="#7476A6"/>
<stop offset="0.234375" stop-color="#7375A6"/>
<stop offset="0.238281" stop-color="#7375A5"/>
<stop offset="0.242188" stop-color="#7274A5"/>
<stop offset="0.246094" stop-color="#7274A4"/>
<stop offset="0.25" stop-color="#7273A4"/>
<stop offset="0.253906" stop-color="#7173A3"/>
<stop offset="0.257812" stop-color="#7172A3"/>
<stop offset="0.261719" stop-color="#7072A2"/>
<stop offset="0.265625" stop-color="#7071A2"/>
<stop offset="0.269531" stop-color="#6F71A1"/>
<stop offset="0.273438" stop-color="#6F71A0"/>
<stop offset="0.277344" stop-color="#6E70A0"/>
<stop offset="0.28125" stop-color="#6E709F"/>
<stop offset="0.285156" stop-color="#6E6F9F"/>
<stop offset="0.289063" stop-color="#6D6F9E"/>
<stop offset="0.292969" stop-color="#6D6E9E"/>
<stop offset="0.296875" stop-color="#6C6E9D"/>
<stop offset="0.300781" stop-color="#6C6D9D"/>
<stop offset="0.304688" stop-color="#6B6D9C"/>
<stop offset="0.308594" stop-color="#6B6C9B"/>
<stop offset="0.3125" stop-color="#6B6C9B"/>
<stop offset="0.316406" stop-color="#6A6B9A"/>
<stop offset="0.320313" stop-color="#6A6B9A"/>
<stop offset="0.324219" stop-color="#696A99"/>
<stop offset="0.328125" stop-color="#696A99"/>
<stop offset="0.332031" stop-color="#686998"/>
<stop offset="0.335938" stop-color="#686998"/>
<stop offset="0.339844" stop-color="#676997"/>
<stop offset="0.34375" stop-color="#676896"/>
<stop offset="0.347656" stop-color="#676896"/>
<stop offset="0.351563" stop-color="#666795"/>
<stop offset="0.355469" stop-color="#666795"/>
<stop offset="0.359375" stop-color="#656694"/>
<stop offset="0.363281" stop-color="#656694"/>
<stop offset="0.367188" stop-color="#646593"/>
<stop offset="0.371094" stop-color="#646593"/>
<stop offset="0.375" stop-color="#636492"/>
<stop offset="0.378906" stop-color="#636492"/>
<stop offset="0.382813" stop-color="#636391"/>
<stop offset="0.386719" stop-color="#626390"/>
<stop offset="0.390625" stop-color="#626290"/>
<stop offset="0.394531" stop-color="#61628F"/>
<stop offset="0.398438" stop-color="#61628F"/>
<stop offset="0.402344" stop-color="#60618E"/>
<stop offset="0.40625" stop-color="#60618E"/>
<stop offset="0.410156" stop-color="#5F608D"/>
<stop offset="0.414063" stop-color="#5F608D"/>
<stop offset="0.417969" stop-color="#5F5F8C"/>
<stop offset="0.421875" stop-color="#5E5F8B"/>
<stop offset="0.425781" stop-color="#5E5E8B"/>
<stop offset="0.429688" stop-color="#5D5E8A"/>
<stop offset="0.433594" stop-color="#5D5D8A"/>
<stop offset="0.4375" stop-color="#5C5D89"/>
<stop offset="0.441406" stop-color="#5C5C89"/>
<stop offset="0.445312" stop-color="#5B5C88"/>
<stop offset="0.449219" stop-color="#5B5B88"/>
<stop offset="0.453125" stop-color="#5B5B87"/>
<stop offset="0.457031" stop-color="#5A5B86"/>
<stop offset="0.460938" stop-color="#5A5A86"/>
<stop offset="0.464844" stop-color="#595A85"/>
<stop offset="0.46875" stop-color="#595985"/>
<stop offset="0.472656" stop-color="#585984"/>
<stop offset="0.476562" stop-color="#585884"/>
<stop offset="0.480469" stop-color="#575883"/>
<stop offset="0.484375" stop-color="#575783"/>
<stop offset="0.488281" stop-color="#575782"/>
<stop offset="0.492188" stop-color="#565682"/>
<stop offset="0.496094" stop-color="#565681"/>
<stop offset="0.5" stop-color="#555580"/>
<stop offset="0.503906" stop-color="#555580"/>
<stop offset="0.507813" stop-color="#54547F"/>
<stop offset="0.511719" stop-color="#54547F"/>
<stop offset="0.515625" stop-color="#53547E"/>
<stop offset="0.519531" stop-color="#53537E"/>
<stop offset="0.523438" stop-color="#53537D"/>
<stop offset="0.527344" stop-color="#52527D"/>
<stop offset="0.53125" stop-color="#52527C"/>
<stop offset="0.535156" stop-color="#51517B"/>
<stop offset="0.539062" stop-color="#51517B"/>
<stop offset="0.542969" stop-color="#50507A"/>
<stop offset="0.546875" stop-color="#50507A"/>
<stop offset="0.550781" stop-color="#4F4F79"/>
<stop offset="0.554688" stop-color="#4F4F79"/>
<stop offset="0.558594" stop-color="#4F4E78"/>
<stop offset="0.5625" stop-color="#4E4E78"/>
<stop offset="0.566406" stop-color="#4E4D77"/>
<stop offset="0.570312" stop-color="#4D4D76"/>
<stop offset="0.574219" stop-color="#4D4C76"/>
<stop offset="0.578125" stop-color="#4C4C75"/>
<stop offset="0.582031" stop-color="#4C4C75"/>
<stop offset="0.585938" stop-color="#4B4B74"/>
<stop offset="0.589844" stop-color="#4B4B74"/>
<stop offset="0.59375" stop-color="#4B4A73"/>
<stop offset="0.597656" stop-color="#4A4A73"/>
<stop offset="0.601563" stop-color="#4A4972"/>
<stop offset="0.605469" stop-color="#494972"/>
<stop offset="0.609375" stop-color="#494871"/>
<stop offset="0.613281" stop-color="#484870"/>
<stop offset="0.617188" stop-color="#484770"/>
<stop offset="0.621094" stop-color="#48476F"/>
<stop offset="0.625" stop-color="#47466F"/>
<stop offset="0.628906" stop-color="#47466E"/>
<stop offset="0.632813" stop-color="#46456E"/>
<stop offset="0.636719" stop-color="#46456D"/>
<stop offset="0.640625" stop-color="#45456D"/>
<stop offset="0.644531" stop-color="#45446C"/>
<stop offset="0.648438" stop-color="#44446B"/>
<stop offset="0.652344" stop-color="#44436B"/>
<stop offset="0.65625" stop-color="#44436A"/>
<stop offset="0.660156" stop-color="#43426A"/>
<stop offset="0.664062" stop-color="#434269"/>
<stop offset="0.667969" stop-color="#424169"/>
<stop offset="0.671875" stop-color="#424168"/>
<stop offset="0.675781" stop-color="#414068"/>
<stop offset="0.679688" stop-color="#414067"/>
<stop offset="0.683594" stop-color="#403F66"/>
<stop offset="0.6875" stop-color="#403F66"/>
<stop offset="0.691406" stop-color="#403E65"/>
<stop offset="0.695312" stop-color="#3F3E65"/>
<stop offset="0.699219" stop-color="#3F3E64"/>
<stop offset="0.703125" stop-color="#3E3D64"/>
<stop offset="0.707031" stop-color="#3E3D63"/>
<stop offset="0.710938" stop-color="#3D3C63"/>
<stop offset="0.714844" stop-color="#3D3C62"/>
<stop offset="0.71875" stop-color="#3C3B62"/>
<stop offset="0.722656" stop-color="#3C3B61"/>
<stop offset="0.726563" stop-color="#3C3A60"/>
<stop offset="0.730469" stop-color="#3B3A60"/>
<stop offset="0.734375" stop-color="#3B395F"/>
<stop offset="0.738281" stop-color="#3A395F"/>
<stop offset="0.742188" stop-color="#3A385E"/>
<stop offset="0.746094" stop-color="#39385E"/>
<stop offset="0.75" stop-color="#39375D"/>
<stop offset="0.753906" stop-color="#38375D"/>
<stop offset="0.757813" stop-color="#38375C"/>
<stop offset="0.761719" stop-color="#38365B"/>
<stop offset="0.765625" stop-color="#37365B"/>
<stop offset="0.769531" stop-color="#37355A"/>
<stop offset="0.773438" stop-color="#36355A"/>
<stop offset="0.777344" stop-color="#363459"/>
<stop offset="0.78125" stop-color="#353459"/>
<stop offset="0.785156" stop-color="#353358"/>
<stop offset="0.789062" stop-color="#343358"/>
<stop offset="0.792969" stop-color="#343257"/>
<stop offset="0.796875" stop-color="#343256"/>
<stop offset="0.800781" stop-color="#333156"/>
<stop offset="0.804688" stop-color="#333155"/>
<stop offset="0.808594" stop-color="#323055"/>
<stop offset="0.8125" stop-color="#323054"/>
<stop offset="0.816406" stop-color="#313054"/>
<stop offset="0.820312" stop-color="#312F53"/>
<stop offset="0.824219" stop-color="#302F53"/>
<stop offset="0.828125" stop-color="#302E52"/>
<stop offset="0.832031" stop-color="#302E52"/>
<stop offset="0.835938" stop-color="#2F2D51"/>
<stop offset="0.839844" stop-color="#2F2D50"/>
<stop offset="0.84375" stop-color="#2E2C50"/>
<stop offset="0.847656" stop-color="#2E2C4F"/>
<stop offset="0.851562" stop-color="#2D2B4F"/>
<stop offset="0.855469" stop-color="#2D2B4E"/>
<stop offset="0.859375" stop-color="#2C2A4E"/>
<stop offset="0.863281" stop-color="#2C2A4D"/>
<stop offset="0.867188" stop-color="#2C294D"/>
<stop offset="0.871094" stop-color="#2B294C"/>
<stop offset="0.875" stop-color="#2B284B"/>
<stop offset="0.878906" stop-color="#2A284B"/>
<stop offset="0.882813" stop-color="#2A284A"/>
<stop offset="0.886719" stop-color="#29274A"/>
<stop offset="0.890625" stop-color="#292749"/>
<stop offset="0.894531" stop-color="#282649"/>
<stop offset="0.898438" stop-color="#282648"/>
<stop offset="0.902344" stop-color="#282548"/>
<stop offset="0.90625" stop-color="#272547"/>
<stop offset="0.910156" stop-color="#272446"/>
<stop offset="0.914063" stop-color="#262446"/>
<stop offset="0.917969" stop-color="#262345"/>
<stop offset="0.921875" stop-color="#252345"/>
<stop offset="0.925781" stop-color="#252244"/>
<stop offset="0.929688" stop-color="#242244"/>
<stop offset="0.933594" stop-color="#242143"/>
<stop offset="0.9375" stop-color="#242143"/>
<stop offset="0.941406" stop-color="#232142"/>
<stop offset="0.945313" stop-color="#232042"/>
<stop offset="0.949219" stop-color="#222041"/>
<stop offset="0.953125" stop-color="#221F40"/>
<stop offset="0.957031" stop-color="#211F40"/>
<stop offset="0.960938" stop-color="#211E3F"/>
<stop offset="0.964844" stop-color="#211E3F"/>
<stop offset="0.96875" stop-color="#201D3E"/>
<stop offset="0.980602" stop-color="#201D3E"/>
<stop offset="1" stop-color="#201D3E"/>
</linearGradient>
<linearGradient id="paint20_linear_2476_449" x1="592.911" y1="456.675" x2="592.944" y2="454.9" gradientUnits="userSpaceOnUse">
<stop stop-color="#33346E"/>
<stop offset="0.0191461" stop-color="#33346E"/>
<stop offset="0.03125" stop-color="#33346E"/>
<stop offset="0.046875" stop-color="#33346E"/>
<stop offset="0.0546875" stop-color="#34356F"/>
<stop offset="0.0625" stop-color="#353670"/>
<stop offset="0.0703125" stop-color="#353670"/>
<stop offset="0.078125" stop-color="#363771"/>
<stop offset="0.0859375" stop-color="#373872"/>
<stop offset="0.09375" stop-color="#373872"/>
<stop offset="0.101563" stop-color="#383973"/>
<stop offset="0.109375" stop-color="#383973"/>
<stop offset="0.117188" stop-color="#393A74"/>
<stop offset="0.125" stop-color="#3A3A75"/>
<stop offset="0.132813" stop-color="#3A3B75"/>
<stop offset="0.140625" stop-color="#3B3C76"/>
<stop offset="0.148438" stop-color="#3B3C76"/>
<stop offset="0.15625" stop-color="#3C3D77"/>
<stop offset="0.164063" stop-color="#3C3D78"/>
<stop offset="0.171875" stop-color="#3D3E78"/>
<stop offset="0.179688" stop-color="#3E3F79"/>
<stop offset="0.1875" stop-color="#3E3F79"/>
<stop offset="0.195313" stop-color="#3F407A"/>
<stop offset="0.203125" stop-color="#3F407B"/>
<stop offset="0.210938" stop-color="#40417B"/>
<stop offset="0.21875" stop-color="#41417C"/>
<stop offset="0.226563" stop-color="#41427C"/>
<stop offset="0.234375" stop-color="#42437D"/>
<stop offset="0.242188" stop-color="#42437E"/>
<stop offset="0.25" stop-color="#43447E"/>
<stop offset="0.257813" stop-color="#44447F"/>
<stop offset="0.265625" stop-color="#44457F"/>
<stop offset="0.273438" stop-color="#454680"/>
<stop offset="0.28125" stop-color="#454681"/>
<stop offset="0.289062" stop-color="#464781"/>
<stop offset="0.296875" stop-color="#474782"/>
<stop offset="0.304688" stop-color="#474882"/>
<stop offset="0.3125" stop-color="#484883"/>
<stop offset="0.320313" stop-color="#484984"/>
<stop offset="0.328125" stop-color="#494A84"/>
<stop offset="0.335938" stop-color="#494A85"/>
<stop offset="0.34375" stop-color="#4A4B85"/>
<stop offset="0.351563" stop-color="#4B4B86"/>
<stop offset="0.359375" stop-color="#4B4C87"/>
<stop offset="0.367188" stop-color="#4C4D87"/>
<stop offset="0.375" stop-color="#4C4D88"/>
<stop offset="0.382813" stop-color="#4D4E88"/>
<stop offset="0.390625" stop-color="#4E4E89"/>
<stop offset="0.398438" stop-color="#4E4F8A"/>
<stop offset="0.40625" stop-color="#4F4F8A"/>
<stop offset="0.414063" stop-color="#4F508B"/>
<stop offset="0.421875" stop-color="#50518B"/>
<stop offset="0.429688" stop-color="#51518C"/>
<stop offset="0.4375" stop-color="#51528D"/>
<stop offset="0.445313" stop-color="#52528D"/>
<stop offset="0.453125" stop-color="#52538E"/>
<stop offset="0.460938" stop-color="#53548E"/>
<stop offset="0.46875" stop-color="#54548F"/>
<stop offset="0.476563" stop-color="#545590"/>
<stop offset="0.484375" stop-color="#555590"/>
<stop offset="0.492188" stop-color="#555691"/>
<stop offset="0.5" stop-color="#565691"/>
<stop offset="0.507812" stop-color="#575792"/>
<stop offset="0.515625" stop-color="#575893"/>
<stop offset="0.523438" stop-color="#585893"/>
<stop offset="0.53125" stop-color="#585994"/>
<stop offset="0.539063" stop-color="#595994"/>
<stop offset="0.546875" stop-color="#595A95"/>
<stop offset="0.554688" stop-color="#5A5B96"/>
<stop offset="0.5625" stop-color="#5B5B96"/>
<stop offset="0.570312" stop-color="#5B5C97"/>
<stop offset="0.578125" stop-color="#5C5C97"/>
<stop offset="0.585938" stop-color="#5C5D98"/>
<stop offset="0.59375" stop-color="#5D5D99"/>
<stop offset="0.601562" stop-color="#5E5E99"/>
<stop offset="0.609375" stop-color="#5E5F9A"/>
<stop offset="0.617188" stop-color="#5F5F9A"/>
<stop offset="0.625" stop-color="#5F609B"/>
<stop offset="0.632812" stop-color="#60609C"/>
<stop offset="0.640625" stop-color="#61619C"/>
<stop offset="0.648438" stop-color="#61629D"/>
<stop offset="0.65625" stop-color="#62629D"/>
<stop offset="0.664063" stop-color="#62639E"/>
<stop offset="0.671875" stop-color="#63639F"/>
<stop offset="0.679688" stop-color="#64649F"/>
<stop offset="0.6875" stop-color="#6464A0"/>
<stop offset="0.695312" stop-color="#6565A1"/>
<stop offset="0.703125" stop-color="#6566A1"/>
<stop offset="0.710938" stop-color="#6666A2"/>
<stop offset="0.71875" stop-color="#6667A2"/>
<stop offset="0.726562" stop-color="#6767A3"/>
<stop offset="0.734375" stop-color="#6868A4"/>
<stop offset="0.742188" stop-color="#6869A4"/>
<stop offset="0.75" stop-color="#6969A5"/>
<stop offset="0.757813" stop-color="#696AA5"/>
<stop offset="0.765625" stop-color="#6A6AA6"/>
<stop offset="0.773438" stop-color="#6B6BA7"/>
<stop offset="0.78125" stop-color="#6B6BA7"/>
<stop offset="0.789063" stop-color="#6C6CA8"/>
<stop offset="0.796875" stop-color="#6C6DA8"/>
<stop offset="0.804688" stop-color="#6D6DA9"/>
<stop offset="0.8125" stop-color="#6E6EAA"/>
<stop offset="0.820312" stop-color="#6E6EAA"/>
<stop offset="0.828125" stop-color="#6F6FAB"/>
<stop offset="0.835938" stop-color="#6F70AB"/>
<stop offset="0.84375" stop-color="#7070AC"/>
<stop offset="0.851562" stop-color="#7171AD"/>
<stop offset="0.859375" stop-color="#7171AD"/>
<stop offset="0.867188" stop-color="#7272AE"/>
<stop offset="0.875" stop-color="#7272AE"/>
<stop offset="0.882813" stop-color="#7373AF"/>
<stop offset="0.890625" stop-color="#7474B0"/>
<stop offset="0.898438" stop-color="#7474B0"/>
<stop offset="0.90625" stop-color="#7575B1"/>
<stop offset="0.914063" stop-color="#7575B1"/>
<stop offset="0.921875" stop-color="#7676B2"/>
<stop offset="0.929688" stop-color="#7677B3"/>
<stop offset="0.9375" stop-color="#7777B3"/>
<stop offset="0.945312" stop-color="#7878B4"/>
<stop offset="0.953125" stop-color="#7878B4"/>
<stop offset="0.960937" stop-color="#7979B5"/>
<stop offset="0.96875" stop-color="#7979B6"/>
<stop offset="0.980854" stop-color="#7A7AB6"/>
<stop offset="1" stop-color="#7A7AB6"/>
</linearGradient>
<linearGradient id="paint21_linear_2476_449" x1="629.004" y1="457.058" x2="629.049" y2="455.822" gradientUnits="userSpaceOnUse">
<stop stop-color="#6564A4"/>
<stop offset="0.00390625" stop-color="#6564A3"/>
<stop offset="0.0078125" stop-color="#6563A3"/>
<stop offset="0.0117188" stop-color="#6463A2"/>
<stop offset="0.015625" stop-color="#6462A1"/>
<stop offset="0.0195313" stop-color="#6362A1"/>
<stop offset="0.0234375" stop-color="#6361A0"/>
<stop offset="0.0273438" stop-color="#62619F"/>
<stop offset="0.03125" stop-color="#62609F"/>
<stop offset="0.0351563" stop-color="#61609E"/>
<stop offset="0.0390625" stop-color="#61609E"/>
<stop offset="0.0429688" stop-color="#615F9D"/>
<stop offset="0.046875" stop-color="#605F9C"/>
<stop offset="0.0507813" stop-color="#605E9C"/>
<stop offset="0.0546875" stop-color="#5F5E9B"/>
<stop offset="0.0585938" stop-color="#5F5D9B"/>
<stop offset="0.0625" stop-color="#5E5D9A"/>
<stop offset="0.0664063" stop-color="#5E5C99"/>
<stop offset="0.0703125" stop-color="#5E5C99"/>
<stop offset="0.0742188" stop-color="#5D5B98"/>
<stop offset="0.078125" stop-color="#5D5B97"/>
<stop offset="0.0820313" stop-color="#5C5B97"/>
<stop offset="0.0859375" stop-color="#5C5A96"/>
<stop offset="0.0898438" stop-color="#5B5A96"/>
<stop offset="0.09375" stop-color="#5B5995"/>
<stop offset="0.0976562" stop-color="#5A5994"/>
<stop offset="0.101562" stop-color="#5A5894"/>
<stop offset="0.105469" stop-color="#5A5893"/>
<stop offset="0.109375" stop-color="#595792"/>
<stop offset="0.113281" stop-color="#595792"/>
<stop offset="0.117188" stop-color="#585691"/>
<stop offset="0.121094" stop-color="#585691"/>
<stop offset="0.125" stop-color="#575590"/>
<stop offset="0.128906" stop-color="#57558F"/>
<stop offset="0.132813" stop-color="#57558F"/>
<stop offset="0.136719" stop-color="#56548E"/>
<stop offset="0.140625" stop-color="#56548E"/>
<stop offset="0.144531" stop-color="#55538D"/>
<stop offset="0.148438" stop-color="#55538C"/>
<stop offset="0.152344" stop-color="#54528C"/>
<stop offset="0.15625" stop-color="#54528B"/>
<stop offset="0.160156" stop-color="#54518A"/>
<stop offset="0.164063" stop-color="#53518A"/>
<stop offset="0.167969" stop-color="#535089"/>
<stop offset="0.171875" stop-color="#525089"/>
<stop offset="0.175781" stop-color="#524F88"/>
<stop offset="0.179688" stop-color="#514F87"/>
<stop offset="0.183594" stop-color="#514F87"/>
<stop offset="0.1875" stop-color="#504E86"/>
<stop offset="0.191406" stop-color="#504E85"/>
<stop offset="0.195313" stop-color="#504D85"/>
<stop offset="0.199219" stop-color="#4F4D84"/>
<stop offset="0.203125" stop-color="#4F4C84"/>
<stop offset="0.207031" stop-color="#4E4C83"/>
<stop offset="0.210938" stop-color="#4E4B82"/>
<stop offset="0.214844" stop-color="#4D4B82"/>
<stop offset="0.21875" stop-color="#4D4A81"/>
<stop offset="0.222656" stop-color="#4D4A80"/>
<stop offset="0.226562" stop-color="#4C4980"/>
<stop offset="0.230469" stop-color="#4C497F"/>
<stop offset="0.234375" stop-color="#4B497F"/>
<stop offset="0.238281" stop-color="#4B487E"/>
<stop offset="0.242188" stop-color="#4A487D"/>
<stop offset="0.246094" stop-color="#4A477D"/>
<stop offset="0.25" stop-color="#4A477C"/>
<stop offset="0.253906" stop-color="#49467C"/>
<stop offset="0.257813" stop-color="#49467B"/>
<stop offset="0.261719" stop-color="#48457A"/>
<stop offset="0.265625" stop-color="#48457A"/>
<stop offset="0.269531" stop-color="#474479"/>
<stop offset="0.273438" stop-color="#474478"/>
<stop offset="0.277344" stop-color="#464478"/>
<stop offset="0.28125" stop-color="#464377"/>
<stop offset="0.285156" stop-color="#464377"/>
<stop offset="0.289063" stop-color="#454276"/>
<stop offset="0.292969" stop-color="#454275"/>
<stop offset="0.296875" stop-color="#444175"/>
<stop offset="0.300781" stop-color="#444174"/>
<stop offset="0.304688" stop-color="#434073"/>
<stop offset="0.308594" stop-color="#434073"/>
<stop offset="0.3125" stop-color="#433F72"/>
<stop offset="0.316406" stop-color="#423F72"/>
<stop offset="0.320313" stop-color="#423E71"/>
<stop offset="0.324219" stop-color="#413E70"/>
<stop offset="0.328125" stop-color="#413E70"/>
<stop offset="0.332031" stop-color="#403D6F"/>
<stop offset="0.335938" stop-color="#403D6F"/>
<stop offset="0.339844" stop-color="#3F3C6E"/>
<stop offset="0.34375" stop-color="#3F3C6D"/>
<stop offset="0.347656" stop-color="#3F3B6D"/>
<stop offset="0.351562" stop-color="#3E3B6C"/>
<stop offset="0.355469" stop-color="#3E3A6B"/>
<stop offset="0.359375" stop-color="#3D3A6B"/>
<stop offset="0.363281" stop-color="#3D396A"/>
<stop offset="0.367188" stop-color="#3C396A"/>
<stop offset="0.371094" stop-color="#3C3869"/>
<stop offset="0.375" stop-color="#3C3868"/>
<stop offset="0.378906" stop-color="#3B3868"/>
<stop offset="0.382812" stop-color="#3B3767"/>
<stop offset="0.386719" stop-color="#3A3766"/>
<stop offset="0.390625" stop-color="#3A3666"/>
<stop offset="0.394531" stop-color="#393665"/>
<stop offset="0.398438" stop-color="#393565"/>
<stop offset="0.402344" stop-color="#393564"/>
<stop offset="0.40625" stop-color="#383463"/>
<stop offset="0.410156" stop-color="#383463"/>
<stop offset="0.414062" stop-color="#373362"/>
<stop offset="0.417969" stop-color="#373361"/>
<stop offset="0.421875" stop-color="#363361"/>
<stop offset="0.425781" stop-color="#363260"/>
<stop offset="0.429688" stop-color="#353260"/>
<stop offset="0.433594" stop-color="#35315F"/>
<stop offset="0.4375" stop-color="#35315E"/>
<stop offset="0.441406" stop-color="#34305E"/>
<stop offset="0.445312" stop-color="#34305D"/>
<stop offset="0.449219" stop-color="#332F5D"/>
<stop offset="0.453125" stop-color="#332F5C"/>
<stop offset="0.457031" stop-color="#322E5B"/>
<stop offset="0.460938" stop-color="#322E5B"/>
<stop offset="0.464844" stop-color="#322D5A"/>
<stop offset="0.46875" stop-color="#312D59"/>
<stop offset="0.472656" stop-color="#312D59"/>
<stop offset="0.476562" stop-color="#302C58"/>
<stop offset="0.480469" stop-color="#302C58"/>
<stop offset="0.484375" stop-color="#2F2B57"/>
<stop offset="0.488281" stop-color="#2F2B56"/>
<stop offset="0.492188" stop-color="#2F2A56"/>
<stop offset="0.496094" stop-color="#2E2A55"/>
<stop offset="0.5" stop-color="#2E2954"/>
<stop offset="0.503906" stop-color="#2D2954"/>
<stop offset="0.507812" stop-color="#2D2853"/>
<stop offset="0.511719" stop-color="#2C2853"/>
<stop offset="0.515625" stop-color="#2C2752"/>
<stop offset="0.519531" stop-color="#2B2751"/>
<stop offset="0.523438" stop-color="#2B2751"/>
<stop offset="0.527344" stop-color="#2B2650"/>
<stop offset="0.53125" stop-color="#2A264F"/>
<stop offset="0.535156" stop-color="#2A254F"/>
<stop offset="0.539063" stop-color="#29254E"/>
<stop offset="0.542969" stop-color="#29244E"/>
<stop offset="0.546875" stop-color="#28244D"/>
<stop offset="0.550781" stop-color="#28234C"/>
<stop offset="0.554688" stop-color="#28234C"/>
<stop offset="0.558594" stop-color="#27224B"/>
<stop offset="0.5625" stop-color="#27224B"/>
<stop offset="0.566406" stop-color="#26224A"/>
<stop offset="0.570313" stop-color="#262149"/>
<stop offset="0.574219" stop-color="#252149"/>
<stop offset="0.578125" stop-color="#252048"/>
<stop offset="0.582031" stop-color="#242047"/>
<stop offset="0.585938" stop-color="#241F47"/>
<stop offset="0.589844" stop-color="#241F46"/>
<stop offset="0.59375" stop-color="#231E46"/>
<stop offset="0.597656" stop-color="#231E45"/>
<stop offset="0.601563" stop-color="#221D44"/>
<stop offset="0.605469" stop-color="#221D44"/>
<stop offset="0.609375" stop-color="#211C43"/>
<stop offset="0.613281" stop-color="#211C42"/>
<stop offset="0.617188" stop-color="#211C42"/>
<stop offset="0.621094" stop-color="#201B41"/>
<stop offset="0.625" stop-color="#201B41"/>
<stop offset="0.628906" stop-color="#1F1A40"/>
<stop offset="0.632813" stop-color="#1F1A3F"/>
<stop offset="0.636719" stop-color="#1E193F"/>
<stop offset="0.640625" stop-color="#1E193E"/>
<stop offset="0.644531" stop-color="#1E183E"/>
<stop offset="0.648438" stop-color="#1D183D"/>
<stop offset="0.652344" stop-color="#1D173C"/>
<stop offset="0.65625" stop-color="#1C173C"/>
<stop offset="0.660156" stop-color="#1C163B"/>
<stop offset="0.664063" stop-color="#1B163A"/>
<stop offset="0.667969" stop-color="#1B163A"/>
<stop offset="0.671875" stop-color="#1A1539"/>
<stop offset="0.675781" stop-color="#1A1539"/>
<stop offset="0.679688" stop-color="#1A1438"/>
<stop offset="0.683594" stop-color="#191437"/>
<stop offset="0.6875" stop-color="#191337"/>
<stop offset="0.691406" stop-color="#181336"/>
<stop offset="0.695313" stop-color="#181235"/>
<stop offset="0.699219" stop-color="#171235"/>
<stop offset="0.703125" stop-color="#171134"/>
<stop offset="0.707031" stop-color="#171134"/>
<stop offset="0.710938" stop-color="#161133"/>
<stop offset="0.714844" stop-color="#161032"/>
<stop offset="0.71875" stop-color="#151032"/>
<stop offset="0.722656" stop-color="#150F31"/>
<stop offset="0.726562" stop-color="#140F30"/>
<stop offset="0.730469" stop-color="#140E30"/>
<stop offset="0.734375" stop-color="#140E2F"/>
<stop offset="0.738281" stop-color="#130D2F"/>
<stop offset="0.742188" stop-color="#130D2E"/>
<stop offset="0.746094" stop-color="#120C2D"/>
<stop offset="0.75" stop-color="#120C2D"/>
<stop offset="0.753906" stop-color="#110B2C"/>
<stop offset="0.757812" stop-color="#110B2C"/>
<stop offset="0.761719" stop-color="#100B2B"/>
<stop offset="0.765625" stop-color="#100A2A"/>
<stop offset="0.769531" stop-color="#100A2A"/>
<stop offset="0.773438" stop-color="#0F0929"/>
<stop offset="0.777344" stop-color="#0F0928"/>
<stop offset="0.78125" stop-color="#0E0828"/>
<stop offset="0.785156" stop-color="#0E0827"/>
<stop offset="0.789062" stop-color="#0D0727"/>
<stop offset="0.796875" stop-color="#0D0726"/>
<stop offset="0.8125" stop-color="#0D0726"/>
<stop offset="0.875" stop-color="#0D0726"/>
<stop offset="0.879973" stop-color="#0D0726"/>
<stop offset="1" stop-color="#0D0726"/>
</linearGradient>
<linearGradient id="paint22_linear_2476_449" x1="678.475" y1="547.853" x2="612.841" y2="527.41" gradientUnits="userSpaceOnUse">
<stop stop-color="#2F2E57"/>
<stop offset="0.483726" stop-color="#2F2E57"/>
<stop offset="0.5" stop-color="#2F2E57"/>
<stop offset="0.516274" stop-color="#2F2E57"/>
<stop offset="0.625" stop-color="#2F2E57"/>
<stop offset="0.640625" stop-color="#2F2E57"/>
<stop offset="0.644531" stop-color="#302E59"/>
<stop offset="0.648438" stop-color="#302F59"/>
<stop offset="0.652344" stop-color="#302F5A"/>
<stop offset="0.65625" stop-color="#312F5B"/>
<stop offset="0.660156" stop-color="#312F5C"/>
<stop offset="0.664062" stop-color="#31305D"/>
<stop offset="0.667969" stop-color="#32305D"/>
<stop offset="0.671875" stop-color="#32305E"/>
<stop offset="0.675781" stop-color="#32315F"/>
<stop offset="0.679687" stop-color="#333160"/>
<stop offset="0.683594" stop-color="#333161"/>
<stop offset="0.6875" stop-color="#333162"/>
<stop offset="0.691406" stop-color="#343262"/>
<stop offset="0.695312" stop-color="#343263"/>
<stop offset="0.699219" stop-color="#343264"/>
<stop offset="0.703125" stop-color="#353265"/>
<stop offset="0.707031" stop-color="#353366"/>
<stop offset="0.710938" stop-color="#363366"/>
<stop offset="0.714844" stop-color="#363367"/>
<stop offset="0.71875" stop-color="#363368"/>
<stop offset="0.722656" stop-color="#373469"/>
<stop offset="0.726562" stop-color="#37346A"/>
<stop offset="0.730469" stop-color="#37346A"/>
<stop offset="0.734375" stop-color="#38346B"/>
<stop offset="0.738281" stop-color="#38356C"/>
<stop offset="0.742188" stop-color="#38356D"/>
<stop offset="0.746094" stop-color="#39356E"/>
<stop offset="0.75" stop-color="#39356E"/>
<stop offset="0.753906" stop-color="#39366F"/>
<stop offset="0.757813" stop-color="#3A3670"/>
<stop offset="0.761719" stop-color="#3A3671"/>
<stop offset="0.765625" stop-color="#3A3772"/>
<stop offset="0.769531" stop-color="#3B3773"/>
<stop offset="0.773438" stop-color="#3B3773"/>
<stop offset="0.777344" stop-color="#3B3774"/>
<stop offset="0.78125" stop-color="#3C3875"/>
<stop offset="0.785156" stop-color="#3C3876"/>
<stop offset="0.789062" stop-color="#3C3877"/>
<stop offset="0.792969" stop-color="#3D3877"/>
<stop offset="0.796875" stop-color="#3D3978"/>
<stop offset="0.800781" stop-color="#3D3979"/>
<stop offset="0.804688" stop-color="#3E397A"/>
<stop offset="0.808594" stop-color="#3E397B"/>
<stop offset="0.8125" stop-color="#3F3A7B"/>
<stop offset="0.816406" stop-color="#3F3A7C"/>
<stop offset="0.820312" stop-color="#3F3A7D"/>
<stop offset="0.824219" stop-color="#403A7E"/>
<stop offset="0.828125" stop-color="#403B7F"/>
<stop offset="0.832031" stop-color="#403B7F"/>
<stop offset="0.835938" stop-color="#413B80"/>
<stop offset="0.839844" stop-color="#413C81"/>
<stop offset="0.84375" stop-color="#413C82"/>
<stop offset="0.847656" stop-color="#423C83"/>
<stop offset="0.851562" stop-color="#423C84"/>
<stop offset="0.855469" stop-color="#423D84"/>
<stop offset="0.859375" stop-color="#433D85"/>
<stop offset="0.863281" stop-color="#433D86"/>
<stop offset="0.867188" stop-color="#433D87"/>
<stop offset="0.871094" stop-color="#443E88"/>
<stop offset="0.875" stop-color="#443E88"/>
<stop offset="0.878906" stop-color="#443E89"/>
<stop offset="0.882812" stop-color="#453E8A"/>
<stop offset="0.886719" stop-color="#453F8B"/>
<stop offset="0.890625" stop-color="#453F8C"/>
<stop offset="0.894531" stop-color="#463F8C"/>
<stop offset="0.898438" stop-color="#463F8D"/>
<stop offset="0.902344" stop-color="#46408E"/>
<stop offset="0.90625" stop-color="#47408F"/>
<stop offset="0.9375" stop-color="#47408F"/>
<stop offset="1" stop-color="#47408F"/>
</linearGradient>
<linearGradient id="paint23_linear_2476_449" x1="696.413" y1="563.047" x2="617.75" y2="524.025" gradientUnits="userSpaceOnUse">
<stop stop-color="#2F2E57"/>
<stop offset="0.384017" stop-color="#2F2E57"/>
<stop offset="0.5" stop-color="#2F2E57"/>
<stop offset="0.53125" stop-color="#2F2E57"/>
<stop offset="0.539062" stop-color="#2F2E57"/>
<stop offset="0.542969" stop-color="#2F2E57"/>
<stop offset="0.546875" stop-color="#2F2E58"/>
<stop offset="0.550781" stop-color="#302E59"/>
<stop offset="0.554688" stop-color="#302F59"/>
<stop offset="0.558594" stop-color="#302F5A"/>
<stop offset="0.5625" stop-color="#302F5A"/>
<stop offset="0.566406" stop-color="#312F5B"/>
<stop offset="0.570312" stop-color="#312F5C"/>
<stop offset="0.574219" stop-color="#31305C"/>
<stop offset="0.578125" stop-color="#31305D"/>
<stop offset="0.582031" stop-color="#32305E"/>
<stop offset="0.585938" stop-color="#32305E"/>
<stop offset="0.589844" stop-color="#32305F"/>
<stop offset="0.59375" stop-color="#33315F"/>
<stop offset="0.597656" stop-color="#333160"/>
<stop offset="0.601562" stop-color="#333161"/>
<stop offset="0.605469" stop-color="#333161"/>
<stop offset="0.609375" stop-color="#343162"/>
<stop offset="0.613281" stop-color="#343262"/>
<stop offset="0.617188" stop-color="#343263"/>
<stop offset="0.621094" stop-color="#343264"/>
<stop offset="0.625" stop-color="#353264"/>
<stop offset="0.628906" stop-color="#353265"/>
<stop offset="0.632812" stop-color="#353366"/>
<stop offset="0.636719" stop-color="#353366"/>
<stop offset="0.640625" stop-color="#363367"/>
<stop offset="0.644531" stop-color="#363367"/>
<stop offset="0.648438" stop-color="#363368"/>
<stop offset="0.652344" stop-color="#363469"/>
<stop offset="0.65625" stop-color="#373469"/>
<stop offset="0.660156" stop-color="#37346A"/>
<stop offset="0.664062" stop-color="#37346A"/>
<stop offset="0.667969" stop-color="#38346B"/>
<stop offset="0.671875" stop-color="#38356C"/>
<stop offset="0.675781" stop-color="#38356C"/>
<stop offset="0.679688" stop-color="#38356D"/>
<stop offset="0.683594" stop-color="#39356E"/>
<stop offset="0.6875" stop-color="#39356E"/>
<stop offset="0.691406" stop-color="#39366F"/>
<stop offset="0.695312" stop-color="#39366F"/>
<stop offset="0.699219" stop-color="#3A3670"/>
<stop offset="0.703125" stop-color="#3A3671"/>
<stop offset="0.707031" stop-color="#3A3671"/>
<stop offset="0.710938" stop-color="#3A3772"/>
<stop offset="0.714844" stop-color="#3B3772"/>
<stop offset="0.71875" stop-color="#3B3773"/>
<stop offset="0.722656" stop-color="#3B3774"/>
<stop offset="0.726562" stop-color="#3B3774"/>
<stop offset="0.730469" stop-color="#3C3875"/>
<stop offset="0.734375" stop-color="#3C3876"/>
<stop offset="0.738281" stop-color="#3C3876"/>
<stop offset="0.742188" stop-color="#3D3877"/>
<stop offset="0.746094" stop-color="#3D3877"/>
<stop offset="0.75" stop-color="#3D3978"/>
<stop offset="0.753906" stop-color="#3D3979"/>
<stop offset="0.757812" stop-color="#3E3979"/>
<stop offset="0.761719" stop-color="#3E397A"/>
<stop offset="0.765625" stop-color="#3E397A"/>
<stop offset="0.769531" stop-color="#3E3A7B"/>
<stop offset="0.773438" stop-color="#3F3A7C"/>
<stop offset="0.777344" stop-color="#3F3A7C"/>
<stop offset="0.78125" stop-color="#3F3A7D"/>
<stop offset="0.785156" stop-color="#3F3A7E"/>
<stop offset="0.789062" stop-color="#403B7E"/>
<stop offset="0.792969" stop-color="#403B7F"/>
<stop offset="0.796875" stop-color="#403B7F"/>
<stop offset="0.800781" stop-color="#403B80"/>
<stop offset="0.804688" stop-color="#413B81"/>
<stop offset="0.808594" stop-color="#413C81"/>
<stop offset="0.8125" stop-color="#413C82"/>
<stop offset="0.816406" stop-color="#423C82"/>
<stop offset="0.820312" stop-color="#423C83"/>
<stop offset="0.824219" stop-color="#423C84"/>
<stop offset="0.828125" stop-color="#423D84"/>
<stop offset="0.832031" stop-color="#433D85"/>
<stop offset="0.835937" stop-color="#433D86"/>
<stop offset="0.839844" stop-color="#433D86"/>
<stop offset="0.84375" stop-color="#433D87"/>
<stop offset="0.847656" stop-color="#443E87"/>
<stop offset="0.851562" stop-color="#443E88"/>
<stop offset="0.855469" stop-color="#443E89"/>
<stop offset="0.859375" stop-color="#443E89"/>
<stop offset="0.863281" stop-color="#453E8A"/>
<stop offset="0.867188" stop-color="#453F8A"/>
<stop offset="0.871094" stop-color="#453F8B"/>
<stop offset="0.875" stop-color="#453F8C"/>
<stop offset="0.878906" stop-color="#463F8C"/>
<stop offset="0.882812" stop-color="#463F8D"/>
<stop offset="0.886719" stop-color="#46408E"/>
<stop offset="0.890625" stop-color="#47408E"/>
<stop offset="0.90625" stop-color="#47408F"/>
<stop offset="0.9375" stop-color="#47408F"/>
<stop offset="1" stop-color="#47408F"/>
</linearGradient>
<linearGradient id="paint24_linear_2476_449" x1="640.554" y1="502.283" x2="662.23" y2="408.033" gradientUnits="userSpaceOnUse">
<stop stop-color="#EE2534"/>
<stop offset="0.110544" stop-color="#EE2534"/>
<stop offset="0.125" stop-color="#EE2534"/>
<stop offset="0.1875" stop-color="#EE2534"/>
<stop offset="0.195312" stop-color="#EE2534"/>
<stop offset="0.203125" stop-color="#EE2635"/>
<stop offset="0.210937" stop-color="#EE2736"/>
<stop offset="0.21875" stop-color="#EE2737"/>
<stop offset="0.226562" stop-color="#EE2838"/>
<stop offset="0.234375" stop-color="#EE2939"/>
<stop offset="0.242187" stop-color="#EE2A3A"/>
<stop offset="0.25" stop-color="#EE2A3B"/>
<stop offset="0.257812" stop-color="#EE2B3C"/>
<stop offset="0.265625" stop-color="#EE2C3D"/>
<stop offset="0.273438" stop-color="#EE2D3E"/>
<stop offset="0.28125" stop-color="#EE2D3F"/>
<stop offset="0.289062" stop-color="#EE2E40"/>
<stop offset="0.296875" stop-color="#EE2F41"/>
<stop offset="0.304688" stop-color="#EE3042"/>
<stop offset="0.3125" stop-color="#EE3043"/>
<stop offset="0.320312" stop-color="#EE3144"/>
<stop offset="0.328125" stop-color="#EE3244"/>
<stop offset="0.335937" stop-color="#EE3345"/>
<stop offset="0.34375" stop-color="#EF3346"/>
<stop offset="0.351562" stop-color="#EF3447"/>
<stop offset="0.359375" stop-color="#EF3548"/>
<stop offset="0.367187" stop-color="#EF3649"/>
<stop offset="0.375" stop-color="#EF364A"/>
<stop offset="0.382812" stop-color="#EF374B"/>
<stop offset="0.390625" stop-color="#EF384C"/>
<stop offset="0.398437" stop-color="#EF384D"/>
<stop offset="0.40625" stop-color="#EF394E"/>
<stop offset="0.414062" stop-color="#EF3A4F"/>
<stop offset="0.421875" stop-color="#EF3B50"/>
<stop offset="0.429688" stop-color="#EF3B51"/>
<stop offset="0.4375" stop-color="#EF3C52"/>
<stop offset="0.445312" stop-color="#EF3D53"/>
<stop offset="0.453125" stop-color="#EF3E53"/>
<stop offset="0.460937" stop-color="#EF3E54"/>
<stop offset="0.46875" stop-color="#EF3F55"/>
<stop offset="0.476562" stop-color="#EF3F55"/>
<stop offset="0.484375" stop-color="#EF4056"/>
<stop offset="0.492188" stop-color="#EF4056"/>
<stop offset="0.5" stop-color="#EF4157"/>
<stop offset="0.507812" stop-color="#EF4258"/>
<stop offset="0.515625" stop-color="#EF4258"/>
<stop offset="0.523437" stop-color="#EF4359"/>
<stop offset="0.53125" stop-color="#EF4359"/>
<stop offset="0.539062" stop-color="#EF445A"/>
<stop offset="0.546875" stop-color="#EF455B"/>
<stop offset="0.554687" stop-color="#EF455B"/>
<stop offset="0.5625" stop-color="#EF465C"/>
<stop offset="0.570312" stop-color="#EF465C"/>
<stop offset="0.578125" stop-color="#EF475D"/>
<stop offset="0.585937" stop-color="#EF485E"/>
<stop offset="0.59375" stop-color="#EF485E"/>
<stop offset="0.601562" stop-color="#EF495F"/>
<stop offset="0.609375" stop-color="#EF495F"/>
<stop offset="0.617188" stop-color="#EF4A60"/>
<stop offset="0.625" stop-color="#EF4A61"/>
<stop offset="0.632812" stop-color="#F04B61"/>
<stop offset="0.640625" stop-color="#F04C62"/>
<stop offset="0.648437" stop-color="#F04C62"/>
<stop offset="0.65625" stop-color="#F04D63"/>
<stop offset="0.664062" stop-color="#F04D64"/>
<stop offset="0.671875" stop-color="#F04E64"/>
<stop offset="0.679688" stop-color="#F04F65"/>
<stop offset="0.6875" stop-color="#F04F65"/>
<stop offset="0.695312" stop-color="#F05066"/>
<stop offset="0.703125" stop-color="#F05066"/>
<stop offset="0.710937" stop-color="#F05167"/>
<stop offset="0.71875" stop-color="#F05268"/>
<stop offset="0.726562" stop-color="#F05268"/>
<stop offset="0.734375" stop-color="#F05369"/>
<stop offset="0.742187" stop-color="#F05369"/>
<stop offset="0.75" stop-color="#F0546A"/>
<stop offset="0.757812" stop-color="#F0546B"/>
<stop offset="0.765625" stop-color="#F0556B"/>
<stop offset="0.78125" stop-color="#F0566C"/>
<stop offset="0.8125" stop-color="#F0566C"/>
<stop offset="0.875" stop-color="#F0566C"/>
<stop offset="0.889456" stop-color="#F0566C"/>
<stop offset="1" stop-color="#F0566C"/>
</linearGradient>
<linearGradient id="paint25_linear_2476_449" x1="629.478" y1="479.733" x2="616.698" y2="434.994" gradientUnits="userSpaceOnUse">
<stop stop-color="#FBB677"/>
<stop offset="0.25" stop-color="#FBB677"/>
<stop offset="0.3125" stop-color="#FBB677"/>
<stop offset="0.316406" stop-color="#FBB678"/>
<stop offset="0.320312" stop-color="#FBB778"/>
<stop offset="0.324219" stop-color="#FBB87A"/>
<stop offset="0.328125" stop-color="#FBB87B"/>
<stop offset="0.332031" stop-color="#FBB97C"/>
<stop offset="0.335938" stop-color="#FBBA7D"/>
<stop offset="0.339844" stop-color="#FBBA7E"/>
<stop offset="0.34375" stop-color="#FBBB7F"/>
<stop offset="0.347656" stop-color="#FBBC81"/>
<stop offset="0.351562" stop-color="#FBBC82"/>
<stop offset="0.355469" stop-color="#FBBD83"/>
<stop offset="0.359375" stop-color="#FCBE84"/>
<stop offset="0.363281" stop-color="#FCBE85"/>
<stop offset="0.367188" stop-color="#FCBF86"/>
<stop offset="0.371094" stop-color="#FCC088"/>
<stop offset="0.375" stop-color="#FCC089"/>
<stop offset="0.378906" stop-color="#FCC18A"/>
<stop offset="0.382812" stop-color="#FCC28B"/>
<stop offset="0.386719" stop-color="#FCC28C"/>
<stop offset="0.390625" stop-color="#FCC38D"/>
<stop offset="0.394531" stop-color="#FCC48F"/>
<stop offset="0.398437" stop-color="#FCC490"/>
<stop offset="0.402344" stop-color="#FCC591"/>
<stop offset="0.40625" stop-color="#FCC692"/>
<stop offset="0.410156" stop-color="#FCC693"/>
<stop offset="0.414062" stop-color="#FCC794"/>
<stop offset="0.417969" stop-color="#FCC895"/>
<stop offset="0.421875" stop-color="#FCC897"/>
<stop offset="0.425781" stop-color="#FCC998"/>
<stop offset="0.429687" stop-color="#FCCA99"/>
<stop offset="0.433594" stop-color="#FDCB9A"/>
<stop offset="0.4375" stop-color="#FDCB9B"/>
<stop offset="0.441406" stop-color="#FDCC9C"/>
<stop offset="0.445312" stop-color="#FDCD9E"/>
<stop offset="0.449219" stop-color="#FDCD9F"/>
<stop offset="0.453125" stop-color="#FDCEA0"/>
<stop offset="0.457031" stop-color="#FDCFA1"/>
<stop offset="0.460937" stop-color="#FDCFA2"/>
<stop offset="0.464844" stop-color="#FDD0A3"/>
<stop offset="0.46875" stop-color="#FDD1A5"/>
<stop offset="0.472656" stop-color="#FDD1A6"/>
<stop offset="0.476562" stop-color="#FDD2A7"/>
<stop offset="0.480469" stop-color="#FDD3A8"/>
<stop offset="0.484375" stop-color="#FDD3A9"/>
<stop offset="0.488281" stop-color="#FDD4AA"/>
<stop offset="0.492187" stop-color="#FDD5AC"/>
<stop offset="0.496094" stop-color="#FDD5AD"/>
<stop offset="0.5" stop-color="#FDD6AE"/>
<stop offset="0.503906" stop-color="#FDD7AF"/>
<stop offset="0.507812" stop-color="#FED7B0"/>
<stop offset="0.511719" stop-color="#FED8B1"/>
<stop offset="0.515625" stop-color="#FED9B2"/>
<stop offset="0.519531" stop-color="#FED9B4"/>
<stop offset="0.523438" stop-color="#FEDAB5"/>
<stop offset="0.527344" stop-color="#FEDBB6"/>
<stop offset="0.53125" stop-color="#FEDBB7"/>
<stop offset="0.535156" stop-color="#FEDCB8"/>
<stop offset="0.539062" stop-color="#FEDDB9"/>
<stop offset="0.546875" stop-color="#FEDDBA"/>
<stop offset="0.5625" stop-color="#FEDDBA"/>
<stop offset="0.572012" stop-color="#FEDDBA"/>
<stop offset="0.625" stop-color="#FEDDBA"/>
<stop offset="0.75" stop-color="#FEDDBA"/>
<stop offset="1" stop-color="#FEDDBA"/>
</linearGradient>
<linearGradient id="paint26_linear_2476_449" x1="671.217" y1="455.637" x2="630.874" y2="414.598" gradientUnits="userSpaceOnUse">
<stop stop-color="#EE2534"/>
<stop offset="0.25" stop-color="#EE2534"/>
<stop offset="0.28125" stop-color="#EE2534"/>
<stop offset="0.289063" stop-color="#EE2534"/>
<stop offset="0.292969" stop-color="#EE2736"/>
<stop offset="0.296875" stop-color="#EE2838"/>
<stop offset="0.300781" stop-color="#EE2A3A"/>
<stop offset="0.304688" stop-color="#EE2C3C"/>
<stop offset="0.308594" stop-color="#EE2E3E"/>
<stop offset="0.3125" stop-color="#EE3040"/>
<stop offset="0.316406" stop-color="#EE3142"/>
<stop offset="0.320313" stop-color="#EF3344"/>
<stop offset="0.324219" stop-color="#EF3546"/>
<stop offset="0.328125" stop-color="#EF3748"/>
<stop offset="0.332031" stop-color="#EF384A"/>
<stop offset="0.335938" stop-color="#EF3A4C"/>
<stop offset="0.339844" stop-color="#EF3C4E"/>
<stop offset="0.34375" stop-color="#EF3E50"/>
<stop offset="0.347656" stop-color="#EF4052"/>
<stop offset="0.351563" stop-color="#EF4155"/>
<stop offset="0.355469" stop-color="#EF4357"/>
<stop offset="0.359375" stop-color="#EF4559"/>
<stop offset="0.363281" stop-color="#EF475B"/>
<stop offset="0.367188" stop-color="#EF485D"/>
<stop offset="0.371094" stop-color="#EF4A5F"/>
<stop offset="0.375" stop-color="#F04C61"/>
<stop offset="0.378906" stop-color="#F04E63"/>
<stop offset="0.382812" stop-color="#F05065"/>
<stop offset="0.386719" stop-color="#F05167"/>
<stop offset="0.390625" stop-color="#F05369"/>
<stop offset="0.394531" stop-color="#F0556B"/>
<stop offset="0.398438" stop-color="#F0566C"/>
<stop offset="0.40625" stop-color="#F0576D"/>
<stop offset="0.414063" stop-color="#F0576D"/>
<stop offset="0.421875" stop-color="#F0586E"/>
<stop offset="0.429688" stop-color="#F0596E"/>
<stop offset="0.4375" stop-color="#F05A6F"/>
<stop offset="0.445313" stop-color="#F05B70"/>
<stop offset="0.453125" stop-color="#F05C70"/>
<stop offset="0.460938" stop-color="#F05C71"/>
<stop offset="0.46875" stop-color="#F15D72"/>
<stop offset="0.476562" stop-color="#F15E72"/>
<stop offset="0.484375" stop-color="#F15F73"/>
<stop offset="0.492188" stop-color="#F16073"/>
<stop offset="0.5" stop-color="#F16174"/>
<stop offset="0.628673" stop-color="#F16175"/>
<stop offset="1" stop-color="#F16275"/>
</linearGradient>
<linearGradient id="paint27_linear_2476_449" x1="645.843" y1="399.225" x2="655.398" y2="428.605" gradientUnits="userSpaceOnUse">
<stop stop-color="#FBB677"/>
<stop offset="0.184844" stop-color="#FBB677"/>
<stop offset="0.25" stop-color="#FBB677"/>
<stop offset="0.3125" stop-color="#FBB677"/>
<stop offset="0.34375" stop-color="#FBB677"/>
<stop offset="0.359375" stop-color="#FBB677"/>
<stop offset="0.367188" stop-color="#FBB677"/>
<stop offset="0.371094" stop-color="#FBB778"/>
<stop offset="0.375" stop-color="#FBB87A"/>
<stop offset="0.378906" stop-color="#FBB97B"/>
<stop offset="0.382812" stop-color="#FBBA7D"/>
<stop offset="0.386719" stop-color="#FBBB7F"/>
<stop offset="0.390625" stop-color="#FBBC80"/>
<stop offset="0.394531" stop-color="#FBBC82"/>
<stop offset="0.398437" stop-color="#FBBD84"/>
<stop offset="0.402344" stop-color="#FCBE85"/>
<stop offset="0.40625" stop-color="#FCBF87"/>
<stop offset="0.410156" stop-color="#FCC089"/>
<stop offset="0.414062" stop-color="#FCC18A"/>
<stop offset="0.417969" stop-color="#FCC28C"/>
<stop offset="0.421875" stop-color="#FCC38E"/>
<stop offset="0.425781" stop-color="#FCC48F"/>
<stop offset="0.429688" stop-color="#FCC591"/>
<stop offset="0.433594" stop-color="#FCC693"/>
<stop offset="0.4375" stop-color="#FCC794"/>
<stop offset="0.441406" stop-color="#FCC896"/>
<stop offset="0.445312" stop-color="#FCC998"/>
<stop offset="0.449219" stop-color="#FCCA99"/>
<stop offset="0.453125" stop-color="#FDCB9B"/>
<stop offset="0.457031" stop-color="#FDCC9D"/>
<stop offset="0.460938" stop-color="#FDCD9E"/>
<stop offset="0.464844" stop-color="#FDCEA0"/>
<stop offset="0.46875" stop-color="#FDCFA2"/>
<stop offset="0.472656" stop-color="#FDD0A3"/>
<stop offset="0.476562" stop-color="#FDD1A5"/>
<stop offset="0.480469" stop-color="#FDD2A7"/>
<stop offset="0.484375" stop-color="#FDD3A8"/>
<stop offset="0.488281" stop-color="#FDD4AA"/>
<stop offset="0.492188" stop-color="#FDD5AC"/>
<stop offset="0.496094" stop-color="#FDD6AD"/>
<stop offset="0.5" stop-color="#FDD7AF"/>
<stop offset="0.503906" stop-color="#FED8B1"/>
<stop offset="0.507812" stop-color="#FED9B2"/>
<stop offset="0.511719" stop-color="#FEDAB4"/>
<stop offset="0.515625" stop-color="#FEDBB6"/>
<stop offset="0.519531" stop-color="#FEDCB7"/>
<stop offset="0.523438" stop-color="#FEDDB9"/>
<stop offset="0.53125" stop-color="#FEDDB9"/>
<stop offset="0.5625" stop-color="#FEDDBA"/>
<stop offset="0.625" stop-color="#FEDDBA"/>
<stop offset="0.75" stop-color="#FEDDBA"/>
<stop offset="0.815156" stop-color="#FEDDBA"/>
<stop offset="1" stop-color="#FEDDBA"/>
</linearGradient>
<linearGradient id="paint28_linear_2476_449" x1="660.759" y1="417.486" x2="628.872" y2="376.22" gradientUnits="userSpaceOnUse">
<stop stop-color="#FCC28C"/>
<stop offset="0.25" stop-color="#FCC28C"/>
<stop offset="0.3125" stop-color="#FCC28C"/>
<stop offset="0.316406" stop-color="#FCC28D"/>
<stop offset="0.320312" stop-color="#FCC38D"/>
<stop offset="0.324219" stop-color="#FCC38E"/>
<stop offset="0.328125" stop-color="#FCC48F"/>
<stop offset="0.332031" stop-color="#FCC490"/>
<stop offset="0.335938" stop-color="#FCC591"/>
<stop offset="0.339844" stop-color="#FCC591"/>
<stop offset="0.34375" stop-color="#FCC692"/>
<stop offset="0.347656" stop-color="#FCC693"/>
<stop offset="0.351562" stop-color="#FCC794"/>
<stop offset="0.355469" stop-color="#FCC795"/>
<stop offset="0.359375" stop-color="#FCC896"/>
<stop offset="0.363281" stop-color="#FCC897"/>
<stop offset="0.367188" stop-color="#FCC998"/>
<stop offset="0.371094" stop-color="#FCC998"/>
<stop offset="0.375" stop-color="#FDCA99"/>
<stop offset="0.378906" stop-color="#FDCA9A"/>
<stop offset="0.382812" stop-color="#FDCB9B"/>
<stop offset="0.386719" stop-color="#FDCB9C"/>
<stop offset="0.390625" stop-color="#FDCC9D"/>
<stop offset="0.394531" stop-color="#FDCC9E"/>
<stop offset="0.398437" stop-color="#FDCD9F"/>
<stop offset="0.402344" stop-color="#FDCD9F"/>
<stop offset="0.40625" stop-color="#FDCEA0"/>
<stop offset="0.410156" stop-color="#FDCEA1"/>
<stop offset="0.414062" stop-color="#FDCFA2"/>
<stop offset="0.417969" stop-color="#FDCFA3"/>
<stop offset="0.421875" stop-color="#FDD0A4"/>
<stop offset="0.425781" stop-color="#FDD0A5"/>
<stop offset="0.429688" stop-color="#FDD1A5"/>
<stop offset="0.433594" stop-color="#FDD1A6"/>
<stop offset="0.4375" stop-color="#FDD2A7"/>
<stop offset="0.441406" stop-color="#FDD2A8"/>
<stop offset="0.445312" stop-color="#FDD3A9"/>
<stop offset="0.449219" stop-color="#FDD3AA"/>
<stop offset="0.453125" stop-color="#FDD4AB"/>
<stop offset="0.457031" stop-color="#FDD4AC"/>
<stop offset="0.460938" stop-color="#FDD5AC"/>
<stop offset="0.464844" stop-color="#FDD5AD"/>
<stop offset="0.46875" stop-color="#FDD6AE"/>
<stop offset="0.472656" stop-color="#FDD6AF"/>
<stop offset="0.476562" stop-color="#FED7B0"/>
<stop offset="0.480469" stop-color="#FED7B1"/>
<stop offset="0.484375" stop-color="#FED8B2"/>
<stop offset="0.488281" stop-color="#FED8B2"/>
<stop offset="0.492187" stop-color="#FED9B3"/>
<stop offset="0.496094" stop-color="#FED9B4"/>
<stop offset="0.5" stop-color="#FEDAB5"/>
<stop offset="0.503906" stop-color="#FEDAB6"/>
<stop offset="0.507812" stop-color="#FEDBB7"/>
<stop offset="0.511719" stop-color="#FEDBB8"/>
<stop offset="0.515625" stop-color="#FEDBB9"/>
<stop offset="0.519531" stop-color="#FEDCB9"/>
<stop offset="0.523438" stop-color="#FEDCBA"/>
<stop offset="0.527344" stop-color="#FEDDBB"/>
<stop offset="0.53125" stop-color="#FEDDBC"/>
<stop offset="0.535156" stop-color="#FEDEBD"/>
<stop offset="0.539062" stop-color="#FEDEBE"/>
<stop offset="0.542969" stop-color="#FEDFBF"/>
<stop offset="0.546875" stop-color="#FEDFC0"/>
<stop offset="0.550781" stop-color="#FEE0C0"/>
<stop offset="0.554688" stop-color="#FEE0C1"/>
<stop offset="0.558594" stop-color="#FEE1C2"/>
<stop offset="0.5625" stop-color="#FEE1C3"/>
<stop offset="0.566406" stop-color="#FEE2C4"/>
<stop offset="0.570312" stop-color="#FEE2C5"/>
<stop offset="0.574219" stop-color="#FEE3C6"/>
<stop offset="0.578125" stop-color="#FEE3C6"/>
<stop offset="0.582031" stop-color="#FFE4C7"/>
<stop offset="0.585937" stop-color="#FFE4C8"/>
<stop offset="0.589844" stop-color="#FFE5C9"/>
<stop offset="0.59375" stop-color="#FFE5CA"/>
<stop offset="0.597656" stop-color="#FFE6CB"/>
<stop offset="0.601562" stop-color="#FFE6CC"/>
<stop offset="0.605469" stop-color="#FFE7CD"/>
<stop offset="0.609375" stop-color="#FFE7CD"/>
<stop offset="0.613281" stop-color="#FFE8CE"/>
<stop offset="0.617188" stop-color="#FFE8CF"/>
<stop offset="0.621094" stop-color="#FFE9D0"/>
<stop offset="0.625" stop-color="#FFE9D1"/>
<stop offset="0.628906" stop-color="#FFEAD1"/>
<stop offset="0.632812" stop-color="#FFEAD2"/>
<stop offset="0.640625" stop-color="#FFEAD2"/>
<stop offset="0.65625" stop-color="#FFEAD2"/>
<stop offset="0.6875" stop-color="#FFEAD2"/>
<stop offset="0.75" stop-color="#FFEAD2"/>
<stop offset="1" stop-color="#FFEAD2"/>
</linearGradient>
<linearGradient id="paint29_linear_2476_449" x1="644.584" y1="421.961" x2="640.77" y2="369.236" gradientUnits="userSpaceOnUse">
<stop stop-color="#0D0726"/>
<stop offset="0.059747" stop-color="#0D0726"/>
<stop offset="0.25" stop-color="#0D0726"/>
<stop offset="0.28125" stop-color="#0D0726"/>
<stop offset="0.296875" stop-color="#0D0726"/>
<stop offset="0.304688" stop-color="#0E0827"/>
<stop offset="0.3125" stop-color="#0E0828"/>
<stop offset="0.320312" stop-color="#0E0929"/>
<stop offset="0.328125" stop-color="#0F0929"/>
<stop offset="0.335938" stop-color="#0F092A"/>
<stop offset="0.34375" stop-color="#100A2B"/>
<stop offset="0.351562" stop-color="#100A2B"/>
<stop offset="0.359375" stop-color="#100B2C"/>
<stop offset="0.367188" stop-color="#110B2D"/>
<stop offset="0.375" stop-color="#110C2E"/>
<stop offset="0.382813" stop-color="#110C2E"/>
<stop offset="0.390625" stop-color="#120D2F"/>
<stop offset="0.398438" stop-color="#120D30"/>
<stop offset="0.40625" stop-color="#120D30"/>
<stop offset="0.414063" stop-color="#130E31"/>
<stop offset="0.421875" stop-color="#130E32"/>
<stop offset="0.429688" stop-color="#140F32"/>
<stop offset="0.4375" stop-color="#140F33"/>
<stop offset="0.445313" stop-color="#141034"/>
<stop offset="0.453125" stop-color="#151035"/>
<stop offset="0.460938" stop-color="#151035"/>
<stop offset="0.46875" stop-color="#151136"/>
<stop offset="0.476563" stop-color="#161137"/>
<stop offset="0.484375" stop-color="#161237"/>
<stop offset="0.492188" stop-color="#171238"/>
<stop offset="0.5" stop-color="#171339"/>
<stop offset="0.507813" stop-color="#171339"/>
<stop offset="0.515625" stop-color="#18133A"/>
<stop offset="0.523438" stop-color="#18143B"/>
<stop offset="0.53125" stop-color="#18143B"/>
<stop offset="0.539063" stop-color="#19153C"/>
<stop offset="0.546875" stop-color="#19153D"/>
<stop offset="0.554688" stop-color="#19163E"/>
<stop offset="0.5625" stop-color="#1A163E"/>
<stop offset="0.570313" stop-color="#1A173F"/>
<stop offset="0.578125" stop-color="#1B1740"/>
<stop offset="0.585938" stop-color="#1B1740"/>
<stop offset="0.59375" stop-color="#1B1841"/>
<stop offset="0.601563" stop-color="#1C1842"/>
<stop offset="0.609375" stop-color="#1C1942"/>
<stop offset="0.617188" stop-color="#1C1943"/>
<stop offset="0.625" stop-color="#1D1A44"/>
<stop offset="0.632813" stop-color="#1D1A44"/>
<stop offset="0.640625" stop-color="#1D1A45"/>
<stop offset="0.648438" stop-color="#1E1B46"/>
<stop offset="0.65625" stop-color="#1E1B47"/>
<stop offset="0.664063" stop-color="#1F1C47"/>
<stop offset="0.671875" stop-color="#1F1C48"/>
<stop offset="0.679688" stop-color="#1F1D49"/>
<stop offset="0.6875" stop-color="#201D49"/>
<stop offset="0.695313" stop-color="#201D4A"/>
<stop offset="0.703125" stop-color="#201E4B"/>
<stop offset="0.710938" stop-color="#211E4B"/>
<stop offset="0.71875" stop-color="#211F4C"/>
<stop offset="0.726563" stop-color="#221F4D"/>
<stop offset="0.734375" stop-color="#22204D"/>
<stop offset="0.742188" stop-color="#22204E"/>
<stop offset="0.75" stop-color="#23214F"/>
<stop offset="0.757813" stop-color="#232150"/>
<stop offset="0.765625" stop-color="#232150"/>
<stop offset="0.773438" stop-color="#242251"/>
<stop offset="0.78125" stop-color="#242252"/>
<stop offset="0.789063" stop-color="#242352"/>
<stop offset="0.796875" stop-color="#252353"/>
<stop offset="0.804688" stop-color="#252454"/>
<stop offset="0.8125" stop-color="#262454"/>
<stop offset="0.820312" stop-color="#262455"/>
<stop offset="0.828125" stop-color="#262556"/>
<stop offset="0.835937" stop-color="#272557"/>
<stop offset="0.84375" stop-color="#272657"/>
<stop offset="0.851563" stop-color="#272658"/>
<stop offset="0.859375" stop-color="#282759"/>
<stop offset="0.867188" stop-color="#282759"/>
<stop offset="0.875" stop-color="#28285A"/>
<stop offset="0.882813" stop-color="#29285B"/>
<stop offset="0.890625" stop-color="#29285B"/>
<stop offset="0.898438" stop-color="#2A295C"/>
<stop offset="0.90625" stop-color="#2A295D"/>
<stop offset="0.914063" stop-color="#2A2A5D"/>
<stop offset="0.921875" stop-color="#2B2A5E"/>
<stop offset="0.929688" stop-color="#2B2B5F"/>
<stop offset="0.9375" stop-color="#2B2B60"/>
<stop offset="0.940253" stop-color="#2C2B60"/>
<stop offset="0.945312" stop-color="#2C2C61"/>
<stop offset="0.953125" stop-color="#2C2C61"/>
<stop offset="0.960937" stop-color="#2C2C62"/>
<stop offset="0.96875" stop-color="#2D2D62"/>
<stop offset="0.976563" stop-color="#2D2D63"/>
<stop offset="0.984375" stop-color="#2E2E64"/>
<stop offset="0.992188" stop-color="#2E2E64"/>
<stop offset="1" stop-color="#2E2E65"/>
</linearGradient>
<linearGradient id="paint30_linear_2476_449" x1="532.818" y1="542.832" x2="481.217" y2="553.433" gradientUnits="userSpaceOnUse">
<stop stop-color="#201D3E"/>
<stop offset="0.450097" stop-color="#201D3E"/>
<stop offset="0.5" stop-color="#201D3E"/>
<stop offset="0.53125" stop-color="#201D3E"/>
<stop offset="0.546875" stop-color="#201D3E"/>
<stop offset="0.549903" stop-color="#201D3E"/>
<stop offset="0.554687" stop-color="#201D3F"/>
<stop offset="0.558594" stop-color="#211E3F"/>
<stop offset="0.5625" stop-color="#211E40"/>
<stop offset="0.566406" stop-color="#211E41"/>
<stop offset="0.570312" stop-color="#221F42"/>
<stop offset="0.574219" stop-color="#221F42"/>
<stop offset="0.578125" stop-color="#221F43"/>
<stop offset="0.582031" stop-color="#232044"/>
<stop offset="0.585937" stop-color="#232045"/>
<stop offset="0.589844" stop-color="#242046"/>
<stop offset="0.59375" stop-color="#242146"/>
<stop offset="0.597656" stop-color="#242147"/>
<stop offset="0.601562" stop-color="#252148"/>
<stop offset="0.605469" stop-color="#252249"/>
<stop offset="0.609375" stop-color="#252249"/>
<stop offset="0.613281" stop-color="#26224A"/>
<stop offset="0.617188" stop-color="#26234B"/>
<stop offset="0.621094" stop-color="#27234C"/>
<stop offset="0.625" stop-color="#27234D"/>
<stop offset="0.628906" stop-color="#27244D"/>
<stop offset="0.632812" stop-color="#28244E"/>
<stop offset="0.636719" stop-color="#28244F"/>
<stop offset="0.640625" stop-color="#282550"/>
<stop offset="0.644531" stop-color="#292550"/>
<stop offset="0.648438" stop-color="#292551"/>
<stop offset="0.652344" stop-color="#292652"/>
<stop offset="0.65625" stop-color="#2A2653"/>
<stop offset="0.660156" stop-color="#2A2654"/>
<stop offset="0.664062" stop-color="#2B2754"/>
<stop offset="0.667969" stop-color="#2B2755"/>
<stop offset="0.671875" stop-color="#2B2756"/>
<stop offset="0.675781" stop-color="#2C2857"/>
<stop offset="0.679688" stop-color="#2C2857"/>
<stop offset="0.683594" stop-color="#2C2858"/>
<stop offset="0.6875" stop-color="#2D2959"/>
<stop offset="0.691406" stop-color="#2D295A"/>
<stop offset="0.695312" stop-color="#2E295B"/>
<stop offset="0.699219" stop-color="#2E2A5B"/>
<stop offset="0.703125" stop-color="#2E2A5C"/>
<stop offset="0.707031" stop-color="#2F2A5D"/>
<stop offset="0.710938" stop-color="#2F2B5E"/>
<stop offset="0.714844" stop-color="#2F2B5E"/>
<stop offset="0.71875" stop-color="#302B5F"/>
<stop offset="0.722656" stop-color="#302C60"/>
<stop offset="0.726562" stop-color="#312C61"/>
<stop offset="0.730469" stop-color="#312C62"/>
<stop offset="0.734375" stop-color="#312D62"/>
<stop offset="0.738281" stop-color="#322D63"/>
<stop offset="0.742187" stop-color="#322D64"/>
<stop offset="0.746094" stop-color="#322E65"/>
<stop offset="0.75" stop-color="#332E65"/>
<stop offset="0.753906" stop-color="#332E66"/>
<stop offset="0.757812" stop-color="#342F67"/>
<stop offset="0.761719" stop-color="#342F68"/>
<stop offset="0.765625" stop-color="#342F68"/>
<stop offset="0.769531" stop-color="#353069"/>
<stop offset="0.773437" stop-color="#35306A"/>
<stop offset="0.777344" stop-color="#35306B"/>
<stop offset="0.78125" stop-color="#36316C"/>
<stop offset="0.785156" stop-color="#36316C"/>
<stop offset="0.789062" stop-color="#37316D"/>
<stop offset="0.792969" stop-color="#37326E"/>
<stop offset="0.796875" stop-color="#37326F"/>
<stop offset="0.800781" stop-color="#38326F"/>
<stop offset="0.804687" stop-color="#383370"/>
<stop offset="0.808594" stop-color="#383371"/>
<stop offset="0.8125" stop-color="#393372"/>
<stop offset="0.816406" stop-color="#393473"/>
<stop offset="0.820312" stop-color="#3A3473"/>
<stop offset="0.824219" stop-color="#3A3474"/>
<stop offset="0.828125" stop-color="#3A3575"/>
<stop offset="0.832031" stop-color="#3B3576"/>
<stop offset="0.835937" stop-color="#3B3576"/>
<stop offset="0.839844" stop-color="#3B3677"/>
<stop offset="0.84375" stop-color="#3C3678"/>
<stop offset="0.847656" stop-color="#3C3679"/>
<stop offset="0.851562" stop-color="#3D377A"/>
<stop offset="0.855469" stop-color="#3D377A"/>
<stop offset="0.859375" stop-color="#3D377B"/>
<stop offset="0.863281" stop-color="#3E387C"/>
<stop offset="0.867187" stop-color="#3E387D"/>
<stop offset="0.871094" stop-color="#3E387D"/>
<stop offset="0.875" stop-color="#3F397E"/>
<stop offset="0.878906" stop-color="#3F397F"/>
<stop offset="0.882812" stop-color="#403980"/>
<stop offset="0.886719" stop-color="#403A81"/>
<stop offset="0.890625" stop-color="#403A81"/>
<stop offset="0.894531" stop-color="#413A82"/>
<stop offset="0.898438" stop-color="#413B83"/>
<stop offset="0.902344" stop-color="#413B84"/>
<stop offset="0.90625" stop-color="#423B84"/>
<stop offset="0.910156" stop-color="#423C85"/>
<stop offset="0.914062" stop-color="#433C86"/>
<stop offset="0.917969" stop-color="#433C87"/>
<stop offset="0.921875" stop-color="#433D88"/>
<stop offset="0.925781" stop-color="#443D88"/>
<stop offset="0.929687" stop-color="#443D89"/>
<stop offset="0.933594" stop-color="#443E8A"/>
<stop offset="0.9375" stop-color="#453E8B"/>
<stop offset="0.941406" stop-color="#453E8B"/>
<stop offset="0.945312" stop-color="#463F8C"/>
<stop offset="0.949219" stop-color="#463F8D"/>
<stop offset="0.953125" stop-color="#463F8E"/>
<stop offset="0.957031" stop-color="#47408E"/>
<stop offset="0.960938" stop-color="#47408F"/>
<stop offset="0.96875" stop-color="#47408F"/>
<stop offset="1" stop-color="#47408F"/>
</linearGradient>
<linearGradient id="paint31_linear_2476_449" x1="535.467" y1="572.517" x2="447.001" y2="518.115" gradientUnits="userSpaceOnUse">
<stop stop-color="#201D3E"/>
<stop offset="0.386098" stop-color="#201D3E"/>
<stop offset="0.5" stop-color="#201D3E"/>
<stop offset="0.515625" stop-color="#201D3E"/>
<stop offset="0.523438" stop-color="#201D3E"/>
<stop offset="0.527344" stop-color="#201D3F"/>
<stop offset="0.53125" stop-color="#201E3F"/>
<stop offset="0.535156" stop-color="#211E40"/>
<stop offset="0.539062" stop-color="#211E41"/>
<stop offset="0.542969" stop-color="#221F42"/>
<stop offset="0.546875" stop-color="#221F43"/>
<stop offset="0.550781" stop-color="#232044"/>
<stop offset="0.554688" stop-color="#232045"/>
<stop offset="0.558594" stop-color="#242046"/>
<stop offset="0.5625" stop-color="#242147"/>
<stop offset="0.566406" stop-color="#242147"/>
<stop offset="0.570312" stop-color="#252248"/>
<stop offset="0.574219" stop-color="#252249"/>
<stop offset="0.578125" stop-color="#26224A"/>
<stop offset="0.582031" stop-color="#26234B"/>
<stop offset="0.585938" stop-color="#27234C"/>
<stop offset="0.589844" stop-color="#27244D"/>
<stop offset="0.59375" stop-color="#28244E"/>
<stop offset="0.597656" stop-color="#28244F"/>
<stop offset="0.601562" stop-color="#282550"/>
<stop offset="0.605469" stop-color="#292551"/>
<stop offset="0.609375" stop-color="#292652"/>
<stop offset="0.613281" stop-color="#2A2653"/>
<stop offset="0.617188" stop-color="#2A2653"/>
<stop offset="0.621094" stop-color="#2B2754"/>
<stop offset="0.625" stop-color="#2B2755"/>
<stop offset="0.628906" stop-color="#2C2856"/>
<stop offset="0.632812" stop-color="#2C2857"/>
<stop offset="0.636719" stop-color="#2C2858"/>
<stop offset="0.640625" stop-color="#2D2959"/>
<stop offset="0.644531" stop-color="#2D295A"/>
<stop offset="0.648438" stop-color="#2E2A5B"/>
<stop offset="0.652344" stop-color="#2E2A5C"/>
<stop offset="0.65625" stop-color="#2F2A5D"/>
<stop offset="0.660156" stop-color="#2F2B5E"/>
<stop offset="0.664062" stop-color="#302B5F"/>
<stop offset="0.667969" stop-color="#302B5F"/>
<stop offset="0.671875" stop-color="#302C60"/>
<stop offset="0.675781" stop-color="#312C61"/>
<stop offset="0.679688" stop-color="#312D62"/>
<stop offset="0.683594" stop-color="#322D63"/>
<stop offset="0.6875" stop-color="#322D64"/>
<stop offset="0.691406" stop-color="#332E65"/>
<stop offset="0.695313" stop-color="#332E66"/>
<stop offset="0.699219" stop-color="#342F67"/>
<stop offset="0.703125" stop-color="#342F68"/>
<stop offset="0.707031" stop-color="#342F69"/>
<stop offset="0.710938" stop-color="#35306A"/>
<stop offset="0.714844" stop-color="#35306B"/>
<stop offset="0.71875" stop-color="#36316B"/>
<stop offset="0.722656" stop-color="#36316C"/>
<stop offset="0.726562" stop-color="#37316D"/>
<stop offset="0.730469" stop-color="#37326E"/>
<stop offset="0.734375" stop-color="#38326F"/>
<stop offset="0.738281" stop-color="#383370"/>
<stop offset="0.742188" stop-color="#383371"/>
<stop offset="0.746094" stop-color="#393372"/>
<stop offset="0.75" stop-color="#393473"/>
<stop offset="0.753906" stop-color="#3A3474"/>
<stop offset="0.757813" stop-color="#3A3575"/>
<stop offset="0.761719" stop-color="#3B3576"/>
<stop offset="0.765625" stop-color="#3B3577"/>
<stop offset="0.769531" stop-color="#3C3677"/>
<stop offset="0.773437" stop-color="#3C3678"/>
<stop offset="0.777344" stop-color="#3C3779"/>
<stop offset="0.78125" stop-color="#3D377A"/>
<stop offset="0.785156" stop-color="#3D377B"/>
<stop offset="0.789062" stop-color="#3E387C"/>
<stop offset="0.792969" stop-color="#3E387D"/>
<stop offset="0.796875" stop-color="#3F397E"/>
<stop offset="0.800781" stop-color="#3F397F"/>
<stop offset="0.804688" stop-color="#403980"/>
<stop offset="0.808594" stop-color="#403A81"/>
<stop offset="0.8125" stop-color="#403A82"/>
<stop offset="0.816406" stop-color="#413B83"/>
<stop offset="0.820312" stop-color="#413B83"/>
<stop offset="0.824219" stop-color="#423B84"/>
<stop offset="0.828125" stop-color="#423C85"/>
<stop offset="0.832031" stop-color="#433C86"/>
<stop offset="0.835938" stop-color="#433D87"/>
<stop offset="0.839844" stop-color="#443D88"/>
<stop offset="0.84375" stop-color="#443D89"/>
<stop offset="0.847656" stop-color="#443E8A"/>
<stop offset="0.851562" stop-color="#453E8B"/>
<stop offset="0.855469" stop-color="#453F8C"/>
<stop offset="0.859375" stop-color="#463F8D"/>
<stop offset="0.863281" stop-color="#463F8E"/>
<stop offset="0.867188" stop-color="#47408F"/>
<stop offset="0.875" stop-color="#47408F"/>
<stop offset="1" stop-color="#47408F"/>
</linearGradient>
<linearGradient id="paint32_linear_2476_449" x1="546.497" y1="467.377" x2="482.954" y2="448.336" gradientUnits="userSpaceOnUse">
<stop stop-color="#FAA85D"/>
<stop offset="0.25" stop-color="#FAA85D"/>
<stop offset="0.265625" stop-color="#FAA85D"/>
<stop offset="0.273438" stop-color="#FAA85D"/>
<stop offset="0.277344" stop-color="#FAA960"/>
<stop offset="0.28125" stop-color="#FAAB62"/>
<stop offset="0.285156" stop-color="#FAAC65"/>
<stop offset="0.289062" stop-color="#FAAE67"/>
<stop offset="0.292969" stop-color="#FBAF6A"/>
<stop offset="0.296875" stop-color="#FBB06C"/>
<stop offset="0.300781" stop-color="#FBB26F"/>
<stop offset="0.304688" stop-color="#FBB372"/>
<stop offset="0.308594" stop-color="#FBB574"/>
<stop offset="0.3125" stop-color="#FBB677"/>
<stop offset="0.316406" stop-color="#FBB879"/>
<stop offset="0.320312" stop-color="#FBB97C"/>
<stop offset="0.324219" stop-color="#FBBA7F"/>
<stop offset="0.328125" stop-color="#FCBC81"/>
<stop offset="0.332031" stop-color="#FCBD84"/>
<stop offset="0.335938" stop-color="#FCBF86"/>
<stop offset="0.339844" stop-color="#FCC089"/>
<stop offset="0.34375" stop-color="#FCC18C"/>
<stop offset="0.347656" stop-color="#FCC38E"/>
<stop offset="0.351562" stop-color="#FCC491"/>
<stop offset="0.355469" stop-color="#FCC693"/>
<stop offset="0.359375" stop-color="#FDC796"/>
<stop offset="0.363281" stop-color="#FDC898"/>
<stop offset="0.367188" stop-color="#FDCA9B"/>
<stop offset="0.371094" stop-color="#FDCB9E"/>
<stop offset="0.375" stop-color="#FDCDA0"/>
<stop offset="0.378906" stop-color="#FDCEA3"/>
<stop offset="0.382813" stop-color="#FDD0A5"/>
<stop offset="0.386719" stop-color="#FDD1A8"/>
<stop offset="0.390625" stop-color="#FED2AB"/>
<stop offset="0.394531" stop-color="#FED4AD"/>
<stop offset="0.398438" stop-color="#FED5B0"/>
<stop offset="0.402344" stop-color="#FED6B1"/>
<stop offset="0.40625" stop-color="#FED7B3"/>
<stop offset="0.4375" stop-color="#FED7B3"/>
<stop offset="0.5" stop-color="#FED7B3"/>
<stop offset="0.679767" stop-color="#FED7B3"/>
<stop offset="1" stop-color="#FED7B3"/>
</linearGradient>
<linearGradient id="paint33_linear_2476_449" x1="513.319" y1="488.3" x2="532.795" y2="416.282" gradientUnits="userSpaceOnUse">
<stop stop-color="#0D0726"/>
<stop offset="0.123461" stop-color="#0D0726"/>
<stop offset="0.125" stop-color="#0D0726"/>
<stop offset="0.140625" stop-color="#0D0726"/>
<stop offset="0.148437" stop-color="#0D0727"/>
<stop offset="0.15625" stop-color="#0E0828"/>
<stop offset="0.164062" stop-color="#0E0828"/>
<stop offset="0.171875" stop-color="#0F0929"/>
<stop offset="0.179688" stop-color="#0F092A"/>
<stop offset="0.1875" stop-color="#0F0A2A"/>
<stop offset="0.195312" stop-color="#100A2B"/>
<stop offset="0.203125" stop-color="#100A2C"/>
<stop offset="0.210938" stop-color="#100B2C"/>
<stop offset="0.21875" stop-color="#110B2D"/>
<stop offset="0.226562" stop-color="#110C2E"/>
<stop offset="0.234375" stop-color="#120C2F"/>
<stop offset="0.242187" stop-color="#120D2F"/>
<stop offset="0.25" stop-color="#120D30"/>
<stop offset="0.257812" stop-color="#130E31"/>
<stop offset="0.265625" stop-color="#130E31"/>
<stop offset="0.273438" stop-color="#130E32"/>
<stop offset="0.28125" stop-color="#140F33"/>
<stop offset="0.289062" stop-color="#140F34"/>
<stop offset="0.296875" stop-color="#151034"/>
<stop offset="0.304688" stop-color="#151035"/>
<stop offset="0.3125" stop-color="#151136"/>
<stop offset="0.320312" stop-color="#161136"/>
<stop offset="0.328125" stop-color="#161237"/>
<stop offset="0.335937" stop-color="#161238"/>
<stop offset="0.34375" stop-color="#171238"/>
<stop offset="0.351562" stop-color="#171339"/>
<stop offset="0.359375" stop-color="#18133A"/>
<stop offset="0.367188" stop-color="#18143B"/>
<stop offset="0.375" stop-color="#18143B"/>
<stop offset="0.382812" stop-color="#19153C"/>
<stop offset="0.390625" stop-color="#19153D"/>
<stop offset="0.398438" stop-color="#19163D"/>
<stop offset="0.40625" stop-color="#1A163E"/>
<stop offset="0.414062" stop-color="#1A163F"/>
<stop offset="0.421875" stop-color="#1B1740"/>
<stop offset="0.429688" stop-color="#1B1740"/>
<stop offset="0.4375" stop-color="#1B1841"/>
<stop offset="0.445312" stop-color="#1C1842"/>
<stop offset="0.453125" stop-color="#1C1942"/>
<stop offset="0.460938" stop-color="#1C1943"/>
<stop offset="0.46875" stop-color="#1D1A44"/>
<stop offset="0.476562" stop-color="#1D1A44"/>
<stop offset="0.484375" stop-color="#1D1A45"/>
<stop offset="0.492187" stop-color="#1E1B46"/>
<stop offset="0.5" stop-color="#1E1B47"/>
<stop offset="0.507812" stop-color="#1F1C47"/>
<stop offset="0.515625" stop-color="#1F1C48"/>
<stop offset="0.523438" stop-color="#1F1D49"/>
<stop offset="0.53125" stop-color="#201D49"/>
<stop offset="0.539062" stop-color="#201E4A"/>
<stop offset="0.546875" stop-color="#201E4B"/>
<stop offset="0.554687" stop-color="#211E4C"/>
<stop offset="0.5625" stop-color="#211F4C"/>
<stop offset="0.570312" stop-color="#221F4D"/>
<stop offset="0.578125" stop-color="#22204E"/>
<stop offset="0.585938" stop-color="#22204E"/>
<stop offset="0.59375" stop-color="#23214F"/>
<stop offset="0.601562" stop-color="#232150"/>
<stop offset="0.609375" stop-color="#232251"/>
<stop offset="0.617188" stop-color="#242251"/>
<stop offset="0.625" stop-color="#242252"/>
<stop offset="0.632812" stop-color="#252353"/>
<stop offset="0.640625" stop-color="#252353"/>
<stop offset="0.648438" stop-color="#252454"/>
<stop offset="0.65625" stop-color="#262455"/>
<stop offset="0.664062" stop-color="#262555"/>
<stop offset="0.671875" stop-color="#262556"/>
<stop offset="0.679687" stop-color="#272657"/>
<stop offset="0.6875" stop-color="#272658"/>
<stop offset="0.695312" stop-color="#282658"/>
<stop offset="0.703125" stop-color="#282759"/>
<stop offset="0.710937" stop-color="#28275A"/>
<stop offset="0.71875" stop-color="#29285A"/>
<stop offset="0.726562" stop-color="#29285B"/>
<stop offset="0.734375" stop-color="#29295C"/>
<stop offset="0.742187" stop-color="#2A295D"/>
<stop offset="0.75" stop-color="#2A2A5D"/>
<stop offset="0.757812" stop-color="#2B2A5E"/>
<stop offset="0.765625" stop-color="#2B2A5F"/>
<stop offset="0.773437" stop-color="#2B2B5F"/>
<stop offset="0.78125" stop-color="#2C2B60"/>
<stop offset="0.789062" stop-color="#2C2C61"/>
<stop offset="0.796875" stop-color="#2C2C61"/>
<stop offset="0.804688" stop-color="#2D2D62"/>
<stop offset="0.8125" stop-color="#2D2D63"/>
<stop offset="0.820312" stop-color="#2E2E64"/>
<stop offset="0.828125" stop-color="#2E2E64"/>
<stop offset="0.835938" stop-color="#2E2E65"/>
<stop offset="0.84375" stop-color="#2F2F66"/>
<stop offset="0.851562" stop-color="#2F2F66"/>
<stop offset="0.859375" stop-color="#2F3067"/>
<stop offset="0.867188" stop-color="#303068"/>
<stop offset="0.875" stop-color="#303169"/>
<stop offset="0.876539" stop-color="#313169"/>
<stop offset="0.882812" stop-color="#31316A"/>
<stop offset="0.890625" stop-color="#31326A"/>
<stop offset="0.898438" stop-color="#31326B"/>
<stop offset="0.90625" stop-color="#32326B"/>
<stop offset="0.914062" stop-color="#32336C"/>
<stop offset="0.921875" stop-color="#32336D"/>
<stop offset="0.9375" stop-color="#33346E"/>
<stop offset="1" stop-color="#33346E"/>
</linearGradient>
<linearGradient id="paint34_linear_2476_449" x1="469.345" y1="449.755" x2="458.857" y2="377.381" gradientUnits="userSpaceOnUse">
<stop stop-color="#FAA85D"/>
<stop offset="0.117491" stop-color="#FAA85D"/>
<stop offset="0.5" stop-color="#FAA85D"/>
<stop offset="0.5625" stop-color="#FAA85D"/>
<stop offset="0.578125" stop-color="#FAA85D"/>
<stop offset="0.585938" stop-color="#FAA85E"/>
<stop offset="0.589844" stop-color="#FAA95F"/>
<stop offset="0.59375" stop-color="#FAAA60"/>
<stop offset="0.597656" stop-color="#FAAA61"/>
<stop offset="0.601562" stop-color="#FAAB62"/>
<stop offset="0.605469" stop-color="#FAAB63"/>
<stop offset="0.609375" stop-color="#FAAC64"/>
<stop offset="0.613281" stop-color="#FAAD65"/>
<stop offset="0.617188" stop-color="#FAAD67"/>
<stop offset="0.621094" stop-color="#FAAE68"/>
<stop offset="0.625" stop-color="#FAAE69"/>
<stop offset="0.628906" stop-color="#FAAF6A"/>
<stop offset="0.632812" stop-color="#FBB06B"/>
<stop offset="0.636719" stop-color="#FBB06C"/>
<stop offset="0.640625" stop-color="#FBB16D"/>
<stop offset="0.644531" stop-color="#FBB16E"/>
<stop offset="0.648438" stop-color="#FBB26F"/>
<stop offset="0.652344" stop-color="#FBB270"/>
<stop offset="0.65625" stop-color="#FBB371"/>
<stop offset="0.660156" stop-color="#FBB472"/>
<stop offset="0.664062" stop-color="#FBB473"/>
<stop offset="0.667969" stop-color="#FBB574"/>
<stop offset="0.671875" stop-color="#FBB575"/>
<stop offset="0.675781" stop-color="#FBB676"/>
<stop offset="0.679688" stop-color="#FBB677"/>
<stop offset="0.683594" stop-color="#FBB778"/>
<stop offset="0.6875" stop-color="#FBB87A"/>
<stop offset="0.691406" stop-color="#FBB87B"/>
<stop offset="0.695312" stop-color="#FBB97C"/>
<stop offset="0.699219" stop-color="#FBB97D"/>
<stop offset="0.703125" stop-color="#FBBA7E"/>
<stop offset="0.707031" stop-color="#FBBA7F"/>
<stop offset="0.710938" stop-color="#FCBB80"/>
<stop offset="0.714844" stop-color="#FCBC81"/>
<stop offset="0.71875" stop-color="#FCBC82"/>
<stop offset="0.722656" stop-color="#FCBD83"/>
<stop offset="0.726562" stop-color="#FCBD84"/>
<stop offset="0.730469" stop-color="#FCBE85"/>
<stop offset="0.734375" stop-color="#FCBE86"/>
<stop offset="0.738281" stop-color="#FCBF87"/>
<stop offset="0.742188" stop-color="#FCC088"/>
<stop offset="0.746094" stop-color="#FCC089"/>
<stop offset="0.75" stop-color="#FCC18A"/>
<stop offset="0.753906" stop-color="#FCC18B"/>
<stop offset="0.757812" stop-color="#FCC28D"/>
<stop offset="0.761719" stop-color="#FCC38E"/>
<stop offset="0.765625" stop-color="#FCC38F"/>
<stop offset="0.769531" stop-color="#FCC490"/>
<stop offset="0.773438" stop-color="#FCC491"/>
<stop offset="0.777344" stop-color="#FCC592"/>
<stop offset="0.78125" stop-color="#FCC593"/>
<stop offset="0.785156" stop-color="#FDC694"/>
<stop offset="0.789062" stop-color="#FDC795"/>
<stop offset="0.792969" stop-color="#FDC796"/>
<stop offset="0.796875" stop-color="#FDC897"/>
<stop offset="0.800781" stop-color="#FDC898"/>
<stop offset="0.804688" stop-color="#FDC999"/>
<stop offset="0.808594" stop-color="#FDC99A"/>
<stop offset="0.8125" stop-color="#FDCA9B"/>
<stop offset="0.816406" stop-color="#FDCB9C"/>
<stop offset="0.820312" stop-color="#FDCB9D"/>
<stop offset="0.824219" stop-color="#FDCC9E"/>
<stop offset="0.828125" stop-color="#FDCCA0"/>
<stop offset="0.832031" stop-color="#FDCDA1"/>
<stop offset="0.835937" stop-color="#FDCDA2"/>
<stop offset="0.839844" stop-color="#FDCEA3"/>
<stop offset="0.84375" stop-color="#FDCFA4"/>
<stop offset="0.847656" stop-color="#FDCFA5"/>
<stop offset="0.851562" stop-color="#FDD0A6"/>
<stop offset="0.855469" stop-color="#FDD0A7"/>
<stop offset="0.859375" stop-color="#FDD1A8"/>
<stop offset="0.863281" stop-color="#FED2A9"/>
<stop offset="0.867188" stop-color="#FED2AA"/>
<stop offset="0.871094" stop-color="#FED3AB"/>
<stop offset="0.875" stop-color="#FED3AC"/>
<stop offset="0.878906" stop-color="#FED4AD"/>
<stop offset="0.882812" stop-color="#FED4AE"/>
<stop offset="0.886719" stop-color="#FED5AF"/>
<stop offset="0.890625" stop-color="#FED6B0"/>
<stop offset="0.894531" stop-color="#FED6B1"/>
<stop offset="0.898438" stop-color="#FED7B3"/>
<stop offset="0.90625" stop-color="#FED7B3"/>
<stop offset="0.9375" stop-color="#FED7B3"/>
<stop offset="1" stop-color="#FED7B3"/>
</linearGradient>
<linearGradient id="paint35_linear_2476_449" x1="517.196" y1="419.895" x2="490.85" y2="364.958" gradientUnits="userSpaceOnUse">
<stop stop-color="#FAA85D"/>
<stop offset="0.25" stop-color="#FAA85D"/>
<stop offset="0.265625" stop-color="#FAA85D"/>
<stop offset="0.268555" stop-color="#FAA85D"/>
<stop offset="0.273438" stop-color="#FAA85D"/>
<stop offset="0.277344" stop-color="#FAA95E"/>
<stop offset="0.28125" stop-color="#FAA95F"/>
<stop offset="0.285156" stop-color="#FAAA60"/>
<stop offset="0.289062" stop-color="#FAAA61"/>
<stop offset="0.292969" stop-color="#FAAB62"/>
<stop offset="0.296875" stop-color="#FAAB63"/>
<stop offset="0.300781" stop-color="#FAAC64"/>
<stop offset="0.304688" stop-color="#FAAC65"/>
<stop offset="0.308594" stop-color="#FAAD66"/>
<stop offset="0.3125" stop-color="#FAAD67"/>
<stop offset="0.316406" stop-color="#FAAE68"/>
<stop offset="0.320312" stop-color="#FAAF69"/>
<stop offset="0.324219" stop-color="#FBAF6A"/>
<stop offset="0.328125" stop-color="#FBB06B"/>
<stop offset="0.332031" stop-color="#FBB06C"/>
<stop offset="0.335938" stop-color="#FBB16D"/>
<stop offset="0.339844" stop-color="#FBB16E"/>
<stop offset="0.34375" stop-color="#FBB26F"/>
<stop offset="0.347656" stop-color="#FBB270"/>
<stop offset="0.351562" stop-color="#FBB371"/>
<stop offset="0.355469" stop-color="#FBB472"/>
<stop offset="0.359375" stop-color="#FBB473"/>
<stop offset="0.363281" stop-color="#FBB574"/>
<stop offset="0.367188" stop-color="#FBB575"/>
<stop offset="0.371094" stop-color="#FBB676"/>
<stop offset="0.375" stop-color="#FBB677"/>
<stop offset="0.378906" stop-color="#FBB778"/>
<stop offset="0.382812" stop-color="#FBB779"/>
<stop offset="0.386719" stop-color="#FBB87A"/>
<stop offset="0.390625" stop-color="#FBB87B"/>
<stop offset="0.394531" stop-color="#FBB97C"/>
<stop offset="0.398438" stop-color="#FBBA7D"/>
<stop offset="0.402344" stop-color="#FBBA7E"/>
<stop offset="0.40625" stop-color="#FCBB7F"/>
<stop offset="0.410156" stop-color="#FCBB80"/>
<stop offset="0.414062" stop-color="#FCBC81"/>
<stop offset="0.417969" stop-color="#FCBC82"/>
<stop offset="0.421875" stop-color="#FCBD83"/>
<stop offset="0.425781" stop-color="#FCBD84"/>
<stop offset="0.429687" stop-color="#FCBE85"/>
<stop offset="0.433594" stop-color="#FCBF86"/>
<stop offset="0.4375" stop-color="#FCBF87"/>
<stop offset="0.441406" stop-color="#FCC088"/>
<stop offset="0.445312" stop-color="#FCC089"/>
<stop offset="0.449219" stop-color="#FCC18A"/>
<stop offset="0.453125" stop-color="#FCC18B"/>
<stop offset="0.457031" stop-color="#FCC28C"/>
<stop offset="0.460938" stop-color="#FCC28D"/>
<stop offset="0.464844" stop-color="#FCC38E"/>
<stop offset="0.46875" stop-color="#FCC38F"/>
<stop offset="0.472656" stop-color="#FCC490"/>
<stop offset="0.476563" stop-color="#FCC591"/>
<stop offset="0.480469" stop-color="#FCC592"/>
<stop offset="0.484375" stop-color="#FCC693"/>
<stop offset="0.488281" stop-color="#FDC694"/>
<stop offset="0.492188" stop-color="#FDC795"/>
<stop offset="0.496094" stop-color="#FDC796"/>
<stop offset="0.5" stop-color="#FDC897"/>
<stop offset="0.503906" stop-color="#FDC898"/>
<stop offset="0.507812" stop-color="#FDC999"/>
<stop offset="0.511719" stop-color="#FDCA9A"/>
<stop offset="0.515625" stop-color="#FDCA9B"/>
<stop offset="0.519531" stop-color="#FDCB9C"/>
<stop offset="0.523438" stop-color="#FDCB9D"/>
<stop offset="0.527344" stop-color="#FDCC9E"/>
<stop offset="0.53125" stop-color="#FDCCA0"/>
<stop offset="0.535156" stop-color="#FDCDA1"/>
<stop offset="0.539062" stop-color="#FDCDA2"/>
<stop offset="0.542969" stop-color="#FDCEA3"/>
<stop offset="0.546875" stop-color="#FDCFA4"/>
<stop offset="0.550781" stop-color="#FDCFA5"/>
<stop offset="0.554688" stop-color="#FDD0A6"/>
<stop offset="0.558594" stop-color="#FDD0A7"/>
<stop offset="0.5625" stop-color="#FDD1A8"/>
<stop offset="0.566406" stop-color="#FDD1A9"/>
<stop offset="0.570312" stop-color="#FED2AA"/>
<stop offset="0.574219" stop-color="#FED2AB"/>
<stop offset="0.578125" stop-color="#FED3AC"/>
<stop offset="0.582031" stop-color="#FED3AD"/>
<stop offset="0.585938" stop-color="#FED4AE"/>
<stop offset="0.589844" stop-color="#FED5AF"/>
<stop offset="0.59375" stop-color="#FED5B0"/>
<stop offset="0.597656" stop-color="#FED6B1"/>
<stop offset="0.601562" stop-color="#FED6B2"/>
<stop offset="0.605469" stop-color="#FED7B2"/>
<stop offset="0.609375" stop-color="#FED7B3"/>
<stop offset="0.625" stop-color="#FED7B3"/>
<stop offset="0.731445" stop-color="#FED7B3"/>
<stop offset="0.75" stop-color="#FED7B3"/>
<stop offset="1" stop-color="#FED7B3"/>
</linearGradient>
<linearGradient id="paint36_linear_2476_449" x1="522.863" y1="505" x2="477.068" y2="409.248" gradientUnits="userSpaceOnUse">
<stop stop-color="#2F2E57"/>
<stop offset="0.125" stop-color="#2F2E57"/>
<stop offset="0.15625" stop-color="#2F2E57"/>
<stop offset="0.171875" stop-color="#2F2E57"/>
<stop offset="0.1875" stop-color="#2F2E58"/>
<stop offset="0.203125" stop-color="#2F2E59"/>
<stop offset="0.21875" stop-color="#2F2F5A"/>
<stop offset="0.234375" stop-color="#302F5B"/>
<stop offset="0.242167" stop-color="#302F5C"/>
<stop offset="0.25" stop-color="#302F5C"/>
<stop offset="0.265625" stop-color="#302F5D"/>
<stop offset="0.28125" stop-color="#30305E"/>
<stop offset="0.296875" stop-color="#30305E"/>
<stop offset="0.3125" stop-color="#30305F"/>
<stop offset="0.328125" stop-color="#313060"/>
<stop offset="0.34375" stop-color="#313161"/>
<stop offset="0.359375" stop-color="#313162"/>
<stop offset="0.375" stop-color="#313163"/>
<stop offset="0.390625" stop-color="#313164"/>
<stop offset="0.40625" stop-color="#313265"/>
<stop offset="0.421875" stop-color="#323266"/>
<stop offset="0.4375" stop-color="#323267"/>
<stop offset="0.453125" stop-color="#323268"/>
<stop offset="0.46875" stop-color="#323369"/>
<stop offset="0.484375" stop-color="#32336A"/>
<stop offset="0.5" stop-color="#32336B"/>
<stop offset="0.515625" stop-color="#33346C"/>
<stop offset="0.53125" stop-color="#33346D"/>
<stop offset="0.539062" stop-color="#33346E"/>
<stop offset="0.546875" stop-color="#33346E"/>
<stop offset="0.554687" stop-color="#34346F"/>
<stop offset="0.5625" stop-color="#34356F"/>
<stop offset="0.570312" stop-color="#343570"/>
<stop offset="0.578125" stop-color="#353570"/>
<stop offset="0.585938" stop-color="#353571"/>
<stop offset="0.59375" stop-color="#353572"/>
<stop offset="0.601562" stop-color="#363672"/>
<stop offset="0.609375" stop-color="#363673"/>
<stop offset="0.617187" stop-color="#363673"/>
<stop offset="0.625" stop-color="#373674"/>
<stop offset="0.632812" stop-color="#373674"/>
<stop offset="0.640625" stop-color="#373775"/>
<stop offset="0.648437" stop-color="#383776"/>
<stop offset="0.65625" stop-color="#383776"/>
<stop offset="0.664062" stop-color="#383777"/>
<stop offset="0.671875" stop-color="#393777"/>
<stop offset="0.679687" stop-color="#393878"/>
<stop offset="0.6875" stop-color="#393878"/>
<stop offset="0.695312" stop-color="#3A3879"/>
<stop offset="0.703125" stop-color="#3A387A"/>
<stop offset="0.710937" stop-color="#3A387A"/>
<stop offset="0.71875" stop-color="#3B397B"/>
<stop offset="0.726562" stop-color="#3B397B"/>
<stop offset="0.734375" stop-color="#3B397C"/>
<stop offset="0.742188" stop-color="#3C397C"/>
<stop offset="0.75" stop-color="#3C397D"/>
<stop offset="0.757812" stop-color="#3C3A7E"/>
<stop offset="0.757833" stop-color="#3D3A7E"/>
<stop offset="0.765625" stop-color="#3D3A7E"/>
<stop offset="0.773437" stop-color="#3D3A7F"/>
<stop offset="0.78125" stop-color="#3D3A7F"/>
<stop offset="0.789062" stop-color="#3E3A80"/>
<stop offset="0.796875" stop-color="#3E3B80"/>
<stop offset="0.804688" stop-color="#3E3B81"/>
<stop offset="0.8125" stop-color="#3F3B81"/>
<stop offset="0.820312" stop-color="#3F3B82"/>
<stop offset="0.828125" stop-color="#3F3C83"/>
<stop offset="0.835938" stop-color="#403C83"/>
<stop offset="0.84375" stop-color="#403C84"/>
<stop offset="0.851563" stop-color="#403C84"/>
<stop offset="0.859375" stop-color="#413C85"/>
<stop offset="0.867187" stop-color="#413D85"/>
<stop offset="0.875" stop-color="#413D86"/>
<stop offset="0.882812" stop-color="#423D87"/>
<stop offset="0.890625" stop-color="#423D87"/>
<stop offset="0.898438" stop-color="#433D88"/>
<stop offset="0.90625" stop-color="#433E88"/>
<stop offset="0.914062" stop-color="#433E89"/>
<stop offset="0.921875" stop-color="#443E89"/>
<stop offset="0.929687" stop-color="#443E8A"/>
<stop offset="0.9375" stop-color="#443E8B"/>
<stop offset="0.945312" stop-color="#453F8B"/>
<stop offset="0.953125" stop-color="#453F8C"/>
<stop offset="0.960938" stop-color="#453F8C"/>
<stop offset="0.96875" stop-color="#463F8D"/>
<stop offset="0.976562" stop-color="#463F8D"/>
<stop offset="0.984375" stop-color="#46408E"/>
<stop offset="1" stop-color="#47408F"/>
</linearGradient>
<linearGradient id="paint37_linear_2476_449" x1="518.139" y1="413.704" x2="496.116" y2="353.204" gradientUnits="userSpaceOnUse">
<stop stop-color="#0D0726"/>
<stop offset="0.233338" stop-color="#0D0726"/>
<stop offset="0.25" stop-color="#0D0726"/>
<stop offset="0.375" stop-color="#0D0726"/>
<stop offset="0.4375" stop-color="#0D0726"/>
<stop offset="0.46875" stop-color="#0D0726"/>
<stop offset="0.484375" stop-color="#0D0726"/>
<stop offset="0.488281" stop-color="#0E0727"/>
<stop offset="0.492188" stop-color="#0E0828"/>
<stop offset="0.496094" stop-color="#0F0929"/>
<stop offset="0.5" stop-color="#0F092A"/>
<stop offset="0.503906" stop-color="#100A2B"/>
<stop offset="0.507812" stop-color="#100B2C"/>
<stop offset="0.511719" stop-color="#110B2D"/>
<stop offset="0.515625" stop-color="#110C2E"/>
<stop offset="0.519531" stop-color="#120C2F"/>
<stop offset="0.523438" stop-color="#120D30"/>
<stop offset="0.527344" stop-color="#130E31"/>
<stop offset="0.53125" stop-color="#130E32"/>
<stop offset="0.535156" stop-color="#140F33"/>
<stop offset="0.539062" stop-color="#141034"/>
<stop offset="0.542969" stop-color="#151035"/>
<stop offset="0.546875" stop-color="#151136"/>
<stop offset="0.550781" stop-color="#161237"/>
<stop offset="0.554688" stop-color="#161238"/>
<stop offset="0.558594" stop-color="#171339"/>
<stop offset="0.5625" stop-color="#18133A"/>
<stop offset="0.566406" stop-color="#18143B"/>
<stop offset="0.570312" stop-color="#19153C"/>
<stop offset="0.574219" stop-color="#19153D"/>
<stop offset="0.578125" stop-color="#1A163E"/>
<stop offset="0.582031" stop-color="#1A173F"/>
<stop offset="0.585938" stop-color="#1B1740"/>
<stop offset="0.589844" stop-color="#1B1841"/>
<stop offset="0.59375" stop-color="#1C1842"/>
<stop offset="0.597656" stop-color="#1C1943"/>
<stop offset="0.601562" stop-color="#1D1A44"/>
<stop offset="0.605469" stop-color="#1D1A45"/>
<stop offset="0.609375" stop-color="#1E1B46"/>
<stop offset="0.613281" stop-color="#1E1C47"/>
<stop offset="0.617188" stop-color="#1F1C48"/>
<stop offset="0.621094" stop-color="#201D49"/>
<stop offset="0.625" stop-color="#201D4A"/>
<stop offset="0.628906" stop-color="#211E4B"/>
<stop offset="0.632812" stop-color="#211F4C"/>
<stop offset="0.636719" stop-color="#221F4D"/>
<stop offset="0.640625" stop-color="#22204E"/>
<stop offset="0.644531" stop-color="#23214F"/>
<stop offset="0.648437" stop-color="#232150"/>
<stop offset="0.652344" stop-color="#242251"/>
<stop offset="0.65625" stop-color="#242352"/>
<stop offset="0.660156" stop-color="#252353"/>
<stop offset="0.664062" stop-color="#252454"/>
<stop offset="0.667969" stop-color="#262455"/>
<stop offset="0.671875" stop-color="#262556"/>
<stop offset="0.675781" stop-color="#272657"/>
<stop offset="0.679688" stop-color="#272658"/>
<stop offset="0.683594" stop-color="#282759"/>
<stop offset="0.6875" stop-color="#29285A"/>
<stop offset="0.691406" stop-color="#29285B"/>
<stop offset="0.695312" stop-color="#2A295C"/>
<stop offset="0.699219" stop-color="#2A295D"/>
<stop offset="0.703125" stop-color="#2B2A5E"/>
<stop offset="0.707031" stop-color="#2B2B5F"/>
<stop offset="0.710938" stop-color="#2C2B60"/>
<stop offset="0.714844" stop-color="#2C2C61"/>
<stop offset="0.71875" stop-color="#2D2D62"/>
<stop offset="0.722656" stop-color="#2D2D63"/>
<stop offset="0.726562" stop-color="#2E2E64"/>
<stop offset="0.730469" stop-color="#2E2E65"/>
<stop offset="0.734375" stop-color="#2F2F66"/>
<stop offset="0.738281" stop-color="#2F3067"/>
<stop offset="0.742188" stop-color="#303068"/>
<stop offset="0.746094" stop-color="#303169"/>
<stop offset="0.75" stop-color="#31326A"/>
<stop offset="0.753906" stop-color="#32326B"/>
<stop offset="0.757813" stop-color="#32336C"/>
<stop offset="0.761719" stop-color="#33336D"/>
<stop offset="0.765625" stop-color="#33346E"/>
<stop offset="0.766662" stop-color="#33346E"/>
<stop offset="0.78125" stop-color="#33346E"/>
<stop offset="0.8125" stop-color="#33346E"/>
<stop offset="0.875" stop-color="#33346E"/>
<stop offset="1" stop-color="#33346E"/>
</linearGradient>
<linearGradient id="paint38_linear_2476_449" x1="488.337" y1="398.043" x2="493.887" y2="388.458" gradientUnits="userSpaceOnUse">
<stop stop-color="#FAA85D"/>
<stop offset="0.125" stop-color="#FAA85D"/>
<stop offset="0.1875" stop-color="#FAA85D"/>
<stop offset="0.194072" stop-color="#FAA85D"/>
<stop offset="0.203125" stop-color="#FAA85D"/>
<stop offset="0.210938" stop-color="#FAA85D"/>
<stop offset="0.214844" stop-color="#FAA95E"/>
<stop offset="0.21875" stop-color="#FAA95E"/>
<stop offset="0.222656" stop-color="#FAA95F"/>
<stop offset="0.226562" stop-color="#FAA960"/>
<stop offset="0.230469" stop-color="#FAAA60"/>
<stop offset="0.234375" stop-color="#FAAA61"/>
<stop offset="0.238281" stop-color="#FAAA61"/>
<stop offset="0.242188" stop-color="#FAAB62"/>
<stop offset="0.246094" stop-color="#FAAB62"/>
<stop offset="0.25" stop-color="#FAAB63"/>
<stop offset="0.253906" stop-color="#FAAC63"/>
<stop offset="0.257812" stop-color="#FAAC64"/>
<stop offset="0.261719" stop-color="#FAAC65"/>
<stop offset="0.265625" stop-color="#FAAD65"/>
<stop offset="0.269531" stop-color="#FAAD66"/>
<stop offset="0.273438" stop-color="#FAAD66"/>
<stop offset="0.277344" stop-color="#FAAE67"/>
<stop offset="0.28125" stop-color="#FAAE67"/>
<stop offset="0.285156" stop-color="#FAAE68"/>
<stop offset="0.289062" stop-color="#FAAE68"/>
<stop offset="0.292969" stop-color="#FAAF69"/>
<stop offset="0.296875" stop-color="#FAAF6A"/>
<stop offset="0.300781" stop-color="#FAAF6A"/>
<stop offset="0.304687" stop-color="#FAB06B"/>
<stop offset="0.308594" stop-color="#FBB06B"/>
<stop offset="0.3125" stop-color="#FBB06C"/>
<stop offset="0.316406" stop-color="#FBB16C"/>
<stop offset="0.320312" stop-color="#FBB16D"/>
<stop offset="0.324219" stop-color="#FBB16D"/>
<stop offset="0.328125" stop-color="#FBB26E"/>
<stop offset="0.332031" stop-color="#FBB26F"/>
<stop offset="0.335937" stop-color="#FBB26F"/>
<stop offset="0.339844" stop-color="#FBB370"/>
<stop offset="0.34375" stop-color="#FBB370"/>
<stop offset="0.347656" stop-color="#FBB371"/>
<stop offset="0.351562" stop-color="#FBB371"/>
<stop offset="0.355469" stop-color="#FBB472"/>
<stop offset="0.359375" stop-color="#FBB472"/>
<stop offset="0.363281" stop-color="#FBB473"/>
<stop offset="0.367187" stop-color="#FBB574"/>
<stop offset="0.371094" stop-color="#FBB574"/>
<stop offset="0.375" stop-color="#FBB575"/>
<stop offset="0.378906" stop-color="#FBB675"/>
<stop offset="0.382812" stop-color="#FBB676"/>
<stop offset="0.386719" stop-color="#FBB676"/>
<stop offset="0.390625" stop-color="#FBB777"/>
<stop offset="0.394531" stop-color="#FBB777"/>
<stop offset="0.398437" stop-color="#FBB778"/>
<stop offset="0.402344" stop-color="#FBB879"/>
<stop offset="0.40625" stop-color="#FBB879"/>
<stop offset="0.410156" stop-color="#FBB87A"/>
<stop offset="0.414062" stop-color="#FBB87A"/>
<stop offset="0.417969" stop-color="#FBB97B"/>
<stop offset="0.421875" stop-color="#FBB97B"/>
<stop offset="0.425781" stop-color="#FBB97C"/>
<stop offset="0.429687" stop-color="#FBBA7C"/>
<stop offset="0.433594" stop-color="#FBBA7D"/>
<stop offset="0.4375" stop-color="#FBBA7E"/>
<stop offset="0.441406" stop-color="#FBBB7E"/>
<stop offset="0.445312" stop-color="#FBBB7F"/>
<stop offset="0.449219" stop-color="#FBBB7F"/>
<stop offset="0.453125" stop-color="#FBBC80"/>
<stop offset="0.457031" stop-color="#FBBC80"/>
<stop offset="0.460937" stop-color="#FBBC81"/>
<stop offset="0.464844" stop-color="#FBBD81"/>
<stop offset="0.46875" stop-color="#FCBD82"/>
<stop offset="0.472656" stop-color="#FCBD83"/>
<stop offset="0.476562" stop-color="#FCBD83"/>
<stop offset="0.480469" stop-color="#FCBE84"/>
<stop offset="0.484375" stop-color="#FCBE84"/>
<stop offset="0.488281" stop-color="#FCBE85"/>
<stop offset="0.492187" stop-color="#FCBF85"/>
<stop offset="0.496094" stop-color="#FCBF86"/>
<stop offset="0.5" stop-color="#FCBF86"/>
<stop offset="0.503906" stop-color="#FCC087"/>
<stop offset="0.507812" stop-color="#FCC087"/>
<stop offset="0.511719" stop-color="#FCC088"/>
<stop offset="0.515625" stop-color="#FCC189"/>
<stop offset="0.519531" stop-color="#FCC189"/>
<stop offset="0.523437" stop-color="#FCC18A"/>
<stop offset="0.527344" stop-color="#FCC28A"/>
<stop offset="0.53125" stop-color="#FCC28B"/>
<stop offset="0.535156" stop-color="#FCC28B"/>
<stop offset="0.539062" stop-color="#FCC28C"/>
<stop offset="0.542969" stop-color="#FCC38C"/>
<stop offset="0.546875" stop-color="#FCC38D"/>
<stop offset="0.550781" stop-color="#FCC38E"/>
<stop offset="0.554687" stop-color="#FCC48E"/>
<stop offset="0.558594" stop-color="#FCC48F"/>
<stop offset="0.5625" stop-color="#FCC48F"/>
<stop offset="0.566406" stop-color="#FCC590"/>
<stop offset="0.570312" stop-color="#FCC590"/>
<stop offset="0.574219" stop-color="#FCC591"/>
<stop offset="0.578125" stop-color="#FCC691"/>
<stop offset="0.582031" stop-color="#FCC692"/>
<stop offset="0.585937" stop-color="#FCC693"/>
<stop offset="0.589844" stop-color="#FCC793"/>
<stop offset="0.59375" stop-color="#FCC794"/>
<stop offset="0.597656" stop-color="#FCC794"/>
<stop offset="0.601562" stop-color="#FCC795"/>
<stop offset="0.605469" stop-color="#FCC895"/>
<stop offset="0.609375" stop-color="#FCC896"/>
<stop offset="0.613281" stop-color="#FCC896"/>
<stop offset="0.617187" stop-color="#FCC997"/>
<stop offset="0.621094" stop-color="#FCC998"/>
<stop offset="0.625" stop-color="#FCC998"/>
<stop offset="0.628906" stop-color="#FCCA99"/>
<stop offset="0.632812" stop-color="#FDCA99"/>
<stop offset="0.636719" stop-color="#FDCA9A"/>
<stop offset="0.640625" stop-color="#FDCB9A"/>
<stop offset="0.644531" stop-color="#FDCB9B"/>
<stop offset="0.648437" stop-color="#FDCB9B"/>
<stop offset="0.652344" stop-color="#FDCC9C"/>
<stop offset="0.65625" stop-color="#FDCC9D"/>
<stop offset="0.660156" stop-color="#FDCC9D"/>
<stop offset="0.664062" stop-color="#FDCC9E"/>
<stop offset="0.667969" stop-color="#FDCD9E"/>
<stop offset="0.671875" stop-color="#FDCD9F"/>
<stop offset="0.675781" stop-color="#FDCD9F"/>
<stop offset="0.679687" stop-color="#FDCEA0"/>
<stop offset="0.683594" stop-color="#FDCEA0"/>
<stop offset="0.6875" stop-color="#FDCEA1"/>
<stop offset="0.691406" stop-color="#FDCFA2"/>
<stop offset="0.695312" stop-color="#FDCFA2"/>
<stop offset="0.699219" stop-color="#FDCFA3"/>
<stop offset="0.703125" stop-color="#FDD0A3"/>
<stop offset="0.707031" stop-color="#FDD0A4"/>
<stop offset="0.710937" stop-color="#FDD0A4"/>
<stop offset="0.714844" stop-color="#FDD1A5"/>
<stop offset="0.71875" stop-color="#FDD1A5"/>
<stop offset="0.722656" stop-color="#FDD1A6"/>
<stop offset="0.726562" stop-color="#FDD1A7"/>
<stop offset="0.730469" stop-color="#FDD2A7"/>
<stop offset="0.734375" stop-color="#FDD2A8"/>
<stop offset="0.738281" stop-color="#FDD2A8"/>
<stop offset="0.742187" stop-color="#FDD3A9"/>
<stop offset="0.746094" stop-color="#FDD3A9"/>
<stop offset="0.75" stop-color="#FDD3AA"/>
<stop offset="0.753906" stop-color="#FDD4AA"/>
<stop offset="0.757812" stop-color="#FDD4AB"/>
<stop offset="0.761719" stop-color="#FDD4AC"/>
<stop offset="0.765625" stop-color="#FDD5AC"/>
<stop offset="0.769531" stop-color="#FDD5AD"/>
<stop offset="0.773437" stop-color="#FDD5AD"/>
<stop offset="0.777344" stop-color="#FDD6AE"/>
<stop offset="0.78125" stop-color="#FDD6AE"/>
<stop offset="0.785156" stop-color="#FDD6AF"/>
<stop offset="0.789062" stop-color="#FDD6AF"/>
<stop offset="0.792969" stop-color="#FED7B0"/>
<stop offset="0.796875" stop-color="#FED7B0"/>
<stop offset="0.800781" stop-color="#FED7B1"/>
<stop offset="0.804687" stop-color="#FED8B2"/>
<stop offset="0.808594" stop-color="#FED8B2"/>
<stop offset="0.8125" stop-color="#FED8B3"/>
<stop offset="0.816406" stop-color="#FED9B3"/>
<stop offset="0.820312" stop-color="#FED9B4"/>
<stop offset="0.824219" stop-color="#FED9B4"/>
<stop offset="0.828125" stop-color="#FEDAB5"/>
<stop offset="0.832031" stop-color="#FEDAB5"/>
<stop offset="0.835937" stop-color="#FEDAB6"/>
<stop offset="0.839844" stop-color="#FEDBB7"/>
<stop offset="0.84375" stop-color="#FEDBB7"/>
<stop offset="0.847656" stop-color="#FEDBB8"/>
<stop offset="0.851562" stop-color="#FEDBB8"/>
<stop offset="0.855469" stop-color="#FEDCB9"/>
<stop offset="0.859375" stop-color="#FEDCB9"/>
<stop offset="0.863281" stop-color="#FEDCBA"/>
<stop offset="0.867187" stop-color="#FEDDBA"/>
<stop offset="0.871094" stop-color="#FEDDBB"/>
<stop offset="0.875" stop-color="#FEDDBC"/>
<stop offset="0.878906" stop-color="#FEDEBC"/>
<stop offset="0.882812" stop-color="#FEDEBD"/>
<stop offset="0.886719" stop-color="#FEDEBD"/>
<stop offset="0.890625" stop-color="#FEDFBE"/>
<stop offset="0.894531" stop-color="#FEDFBE"/>
<stop offset="0.898437" stop-color="#FEDFBF"/>
<stop offset="0.902344" stop-color="#FEE0BF"/>
<stop offset="0.90625" stop-color="#FEE0C0"/>
<stop offset="0.910156" stop-color="#FEE0C1"/>
<stop offset="0.914062" stop-color="#FEE0C1"/>
<stop offset="0.917969" stop-color="#FEE1C2"/>
<stop offset="0.921875" stop-color="#FEE1C2"/>
<stop offset="0.925781" stop-color="#FEE1C3"/>
<stop offset="0.929687" stop-color="#FEE2C3"/>
<stop offset="0.933594" stop-color="#FEE2C4"/>
<stop offset="0.9375" stop-color="#FEE2C4"/>
<stop offset="0.941406" stop-color="#FEE3C5"/>
<stop offset="0.945312" stop-color="#FEE3C6"/>
<stop offset="0.949219" stop-color="#FEE3C6"/>
<stop offset="0.953125" stop-color="#FEE4C7"/>
<stop offset="0.957031" stop-color="#FFE4C7"/>
<stop offset="0.960937" stop-color="#FFE4C8"/>
<stop offset="0.964844" stop-color="#FFE5C8"/>
<stop offset="0.96875" stop-color="#FFE5C9"/>
<stop offset="0.972656" stop-color="#FFE5C9"/>
<stop offset="0.976562" stop-color="#FFE5CA"/>
<stop offset="0.980469" stop-color="#FFE6CB"/>
<stop offset="0.984375" stop-color="#FFE6CB"/>
<stop offset="0.988281" stop-color="#FFE6CC"/>
<stop offset="0.992187" stop-color="#FFE7CC"/>
<stop offset="0.996094" stop-color="#FFE7CD"/>
<stop offset="1" stop-color="#FFE7CD"/>
</linearGradient>
<linearGradient id="paint39_linear_2476_449" x1="467.851" y1="446.783" x2="466.859" y2="406.496" gradientUnits="userSpaceOnUse">
<stop stop-color="#201D3E"/>
<stop offset="0.0338242" stop-color="#201D3E"/>
<stop offset="0.0625" stop-color="#201D3E"/>
<stop offset="0.09375" stop-color="#201D3E"/>
<stop offset="0.109375" stop-color="#211E3F"/>
<stop offset="0.125" stop-color="#211E40"/>
<stop offset="0.140625" stop-color="#211F41"/>
<stop offset="0.15625" stop-color="#221F41"/>
<stop offset="0.171875" stop-color="#222042"/>
<stop offset="0.1875" stop-color="#232042"/>
<stop offset="0.203125" stop-color="#232143"/>
<stop offset="0.21875" stop-color="#232144"/>
<stop offset="0.234375" stop-color="#242144"/>
<stop offset="0.25" stop-color="#242245"/>
<stop offset="0.265625" stop-color="#252246"/>
<stop offset="0.28125" stop-color="#252346"/>
<stop offset="0.296875" stop-color="#252347"/>
<stop offset="0.3125" stop-color="#262448"/>
<stop offset="0.328125" stop-color="#262448"/>
<stop offset="0.34375" stop-color="#262449"/>
<stop offset="0.359375" stop-color="#27254A"/>
<stop offset="0.375" stop-color="#27254A"/>
<stop offset="0.390625" stop-color="#28264B"/>
<stop offset="0.40625" stop-color="#28264B"/>
<stop offset="0.421875" stop-color="#28274C"/>
<stop offset="0.4375" stop-color="#29274D"/>
<stop offset="0.453125" stop-color="#29274D"/>
<stop offset="0.46875" stop-color="#2A284E"/>
<stop offset="0.484375" stop-color="#2A284F"/>
<stop offset="0.5" stop-color="#2A294F"/>
<stop offset="0.515625" stop-color="#2B2950"/>
<stop offset="0.53125" stop-color="#2B2A51"/>
<stop offset="0.546875" stop-color="#2C2A51"/>
<stop offset="0.5625" stop-color="#2C2B52"/>
<stop offset="0.578125" stop-color="#2C2B53"/>
<stop offset="0.59375" stop-color="#2D2B53"/>
<stop offset="0.609375" stop-color="#2D2C54"/>
<stop offset="0.625" stop-color="#2D2C55"/>
<stop offset="0.640625" stop-color="#2E2D55"/>
<stop offset="0.65625" stop-color="#2E2D56"/>
<stop offset="0.671875" stop-color="#2F2E56"/>
<stop offset="0.6875" stop-color="#2F2E57"/>
<stop offset="0.703125" stop-color="#2F2E58"/>
<stop offset="0.71875" stop-color="#2F2E59"/>
<stop offset="0.734375" stop-color="#2F2F59"/>
<stop offset="0.75" stop-color="#2F2F5A"/>
<stop offset="0.765625" stop-color="#302F5B"/>
<stop offset="0.78125" stop-color="#302F5C"/>
<stop offset="0.796875" stop-color="#302F5C"/>
<stop offset="0.8125" stop-color="#30305D"/>
<stop offset="0.828125" stop-color="#30305E"/>
<stop offset="0.84375" stop-color="#30305F"/>
<stop offset="0.859375" stop-color="#30305F"/>
<stop offset="0.875" stop-color="#313060"/>
<stop offset="0.890625" stop-color="#313161"/>
<stop offset="0.90625" stop-color="#313162"/>
<stop offset="0.921875" stop-color="#313162"/>
<stop offset="0.9375" stop-color="#313163"/>
<stop offset="0.953125" stop-color="#313164"/>
<stop offset="0.966176" stop-color="#313265"/>
<stop offset="0.96875" stop-color="#313265"/>
<stop offset="0.984375" stop-color="#323265"/>
<stop offset="1" stop-color="#323266"/>
</linearGradient>
<linearGradient id="paint40_linear_2476_449" x1="508.605" y1="420.646" x2="493.812" y2="406.189" gradientUnits="userSpaceOnUse">
<stop stop-color="#201D3E"/>
<stop offset="0.158188" stop-color="#201D3E"/>
<stop offset="0.5" stop-color="#201D3E"/>
<stop offset="0.5625" stop-color="#201D3E"/>
<stop offset="0.59375" stop-color="#201D3E"/>
<stop offset="0.609375" stop-color="#201D3E"/>
<stop offset="0.617188" stop-color="#201D3E"/>
<stop offset="0.621094" stop-color="#201D3F"/>
<stop offset="0.625" stop-color="#201E3F"/>
<stop offset="0.628906" stop-color="#211E40"/>
<stop offset="0.632812" stop-color="#211E40"/>
<stop offset="0.636719" stop-color="#211E41"/>
<stop offset="0.640625" stop-color="#211F41"/>
<stop offset="0.644531" stop-color="#211F42"/>
<stop offset="0.648438" stop-color="#221F43"/>
<stop offset="0.652344" stop-color="#222043"/>
<stop offset="0.65625" stop-color="#222044"/>
<stop offset="0.660156" stop-color="#222044"/>
<stop offset="0.664062" stop-color="#232045"/>
<stop offset="0.667969" stop-color="#232145"/>
<stop offset="0.671875" stop-color="#232146"/>
<stop offset="0.675781" stop-color="#232147"/>
<stop offset="0.679687" stop-color="#242147"/>
<stop offset="0.683594" stop-color="#242248"/>
<stop offset="0.6875" stop-color="#242248"/>
<stop offset="0.691406" stop-color="#242249"/>
<stop offset="0.695312" stop-color="#242349"/>
<stop offset="0.699219" stop-color="#25234A"/>
<stop offset="0.703125" stop-color="#25234B"/>
<stop offset="0.707031" stop-color="#25234B"/>
<stop offset="0.710938" stop-color="#25244C"/>
<stop offset="0.714844" stop-color="#26244C"/>
<stop offset="0.71875" stop-color="#26244D"/>
<stop offset="0.722656" stop-color="#26244D"/>
<stop offset="0.726562" stop-color="#26254E"/>
<stop offset="0.730469" stop-color="#27254F"/>
<stop offset="0.734375" stop-color="#27254F"/>
<stop offset="0.738281" stop-color="#272650"/>
<stop offset="0.742187" stop-color="#272650"/>
<stop offset="0.746094" stop-color="#272651"/>
<stop offset="0.75" stop-color="#282651"/>
<stop offset="0.753906" stop-color="#282752"/>
<stop offset="0.757812" stop-color="#282753"/>
<stop offset="0.761719" stop-color="#282753"/>
<stop offset="0.765625" stop-color="#292854"/>
<stop offset="0.769531" stop-color="#292854"/>
<stop offset="0.773437" stop-color="#292855"/>
<stop offset="0.777344" stop-color="#292856"/>
<stop offset="0.78125" stop-color="#292956"/>
<stop offset="0.785156" stop-color="#2A2957"/>
<stop offset="0.789062" stop-color="#2A2957"/>
<stop offset="0.792969" stop-color="#2A2958"/>
<stop offset="0.796875" stop-color="#2A2A58"/>
<stop offset="0.800781" stop-color="#2B2A59"/>
<stop offset="0.804688" stop-color="#2B2A5A"/>
<stop offset="0.808594" stop-color="#2B2B5A"/>
<stop offset="0.8125" stop-color="#2B2B5B"/>
<stop offset="0.816406" stop-color="#2C2B5B"/>
<stop offset="0.820312" stop-color="#2C2B5C"/>
<stop offset="0.824219" stop-color="#2C2C5C"/>
<stop offset="0.828125" stop-color="#2C2C5D"/>
<stop offset="0.832031" stop-color="#2C2C5E"/>
<stop offset="0.835937" stop-color="#2D2C5E"/>
<stop offset="0.839844" stop-color="#2D2D5F"/>
<stop offset="0.84375" stop-color="#2D2D5F"/>
<stop offset="0.847656" stop-color="#2D2D60"/>
<stop offset="0.851562" stop-color="#2E2E60"/>
<stop offset="0.855469" stop-color="#2E2E61"/>
<stop offset="0.859375" stop-color="#2E2E62"/>
<stop offset="0.863281" stop-color="#2E2E62"/>
<stop offset="0.867187" stop-color="#2F2F63"/>
<stop offset="0.871094" stop-color="#2F2F63"/>
<stop offset="0.875" stop-color="#2F2F64"/>
<stop offset="0.878906" stop-color="#2F2F64"/>
<stop offset="0.882812" stop-color="#2F3065"/>
<stop offset="0.886719" stop-color="#303066"/>
<stop offset="0.890625" stop-color="#303066"/>
<stop offset="0.894531" stop-color="#303167"/>
<stop offset="0.898437" stop-color="#303167"/>
<stop offset="0.902344" stop-color="#313168"/>
<stop offset="0.90625" stop-color="#313168"/>
<stop offset="0.910156" stop-color="#313269"/>
<stop offset="0.914062" stop-color="#31326A"/>
<stop offset="0.917969" stop-color="#32326A"/>
<stop offset="0.921875" stop-color="#32336B"/>
<stop offset="0.925781" stop-color="#32336B"/>
<stop offset="0.929688" stop-color="#32336C"/>
<stop offset="0.933594" stop-color="#32336C"/>
<stop offset="0.9375" stop-color="#33346D"/>
<stop offset="1" stop-color="#33346D"/>
</linearGradient>
<clipPath id="clip0_2476_449">
<rect width="843" height="809" fill="white"/>
</clipPath>
</defs>
</svg>
