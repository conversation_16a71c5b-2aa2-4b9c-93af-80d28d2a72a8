.cheader{
    display: flex;
  padding:  3vw 9vw 2vw 9vw;
  justify-content: space-between;
  font-family:"Poppins";
  font-size: 16px;
  font-weight: 500;
}
.c-content{
  padding: 0vw 9vw 6vw;
  font-family:"Poppins";
  font-size: 16px;
}

.form-container {
  background: #fff;
  padding: 30px;
  max-width: 700px;
  margin: auto;
  border-radius: 10px;
  box-shadow: 0 0 15px rgba(0,0,0,0.2);
  color: black;
}


.form-container h2 {
  text-align: center;
  margin-bottom: 20px;
}

form label {
  display: block;
  margin: 15px 0 5px;
  font-weight: 500;
}

form input[type="text"],
form input[type="email"],
form input[type="tel"],
form input[type="url"],
form textarea,
form select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-sizing: border-box;
}

form textarea {
  height: 100px;
  resize: vertical;
}



.or-divider {
  text-align: center;
  margin: 5px 0;
  font-weight: bold;
  color: #666;
}
.checkbox{
  display: flex; 
  align-items: center; 
  gap: 8px; 
  margin-bottom: 16px;
}


button[type="submit"] {
  background-color: #007bff;
  color: white;
  padding: 8px 15px;
  border: none;
  border-radius: 5px;
  width: auto; 
  font-size: 16px;
  cursor: pointer;
  text-align: center;
}
#ok-btn{
  background-color: #007bff;
  color: white;
  padding: 8px 15px;
  border: none;
  border-radius: 5px;
  width: auto; 
  font-size: 16px;
  cursor: pointer;
  text-align: center;
}
#ok-btn:hover{
  background-color: #0056b3;
}
button[type="submit"]:hover {
  background-color: #0056b3;
}

.upload-box {
  border: 2px solid #ccc;
  padding: 10px 0;
  text-align: center;
  border-radius: 5px;
  cursor: pointer;
  position: relative;
}

.upload-box input[type="file"] {
  border: none;
}
.upload-img{
  width: 40px; 
  height: 40px; 
  display: block; 
  margin: 0 auto;
}
.file-upload-text{
  margin-top: 8px; 
  color: #555;
}

.del-uploads {
  display: none;
  position: absolute;
  top: -6px;
  right: -7px; 
  background: white;
  border: 1px solid grey;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 1px rgba(0,0,0,0.2);
  height: 20px;
  width: 20px;
}
.del-uploads-img{
  width: 16px; 
  height: 16px;
  position: absolute;
  top: 1px;
  left: 1px;
  cursor: pointer;
}

.upload-label{
  cursor: pointer; 
  display: inline-block;
}

 .filename-wrapper {
  position: relative;
  display: inline-block;
}
.upload-filename {
  display: none;
  font-weight: 500;
  border: 1px solid grey;
  border-radius: 8px;
  padding: 6px 20px;
  background-color: #f9f9f9;
}
.other-res{
  display:none; 
  margin-top:10px; 
  max-width:100%;
}
.row-fields{
  display: flex;
  gap: 15px; 
  justify-content: space-between;
}
.c-red{
 color: red;
}
#other-source{
  display:none; 
  margin-top: 10px;
}

.check-desc{
  font-size: 12px;
  margin-top: 16px;
  line-height: normal;
}
#terms{
  margin: 0;
  width: 12px;
  margin-top: -3px;
}
.terms-desc{
  margin: 0;
  font-size: 12px;
  margin-top: -3px;
}
.form-heading{
  font-size: 20px;
  text-align: center;
}
.max-w{
  flex: 1;
}
@media only screen and (max-width:600px) {
  .cheader{
    flex-direction: column;
  }
  #ham-image{
    height: 36px !important;
  }
}
@media (max-width: 1200px){
  .row-fields{
    all:unset;
  }
}

.phone-wrapper {
  position: relative;
  width: 100%;
}

.iti {
  position: relative;
  width: 100%;
}

.iti__country-list {
  position: absolute !important;
  top: 100% !important;
  left: 0 !important;
  width: 312px !important;
  z-index: 9999 !important;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  overflow-x: scroll;
}
