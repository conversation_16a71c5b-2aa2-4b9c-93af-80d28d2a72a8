.search-container {
  position: relative;
  width: 300px;
}

.search-input {
  width: 100%;
  padding: 10px 40px 10px 15px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 25px;
  outline: none;
}

.search-icon {
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  pointer-events: none;
}
.accordion {
  background-color: #eee;
  color: #444;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 9px;
  width: 100%;
  text-align: left;
  border: none;
  outline: none;
  transition: 0.4s;
  font-size: 16px;
}

.ques-arrow-img {
  transition: transform 0.3s ease;
}

.active,
.accordion:hover {
  background-color: #306fbc;
  color: white;
  font-weight: 500;
}
.panel {
  padding: 8px 18px;
  background-color: white;
  display: none;
  overflow: hidden;
  margin-bottom: 0px;
}

.questions {
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  margin: 16px 0;
  overflow: hidden;
}

.accordion.active .ques-arrow-img {
  transform: rotate(180deg);
}
.faq-button {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  background-color: #306fbc;
  min-width: 15%;
  border-radius: 9px;
  min-height: 40px;
  width: 13%;
}

.faq-button a {
  color: white;
  text-decoration: none;
  font-size: 18px;
  font-weight: 500;
}
.panel p {
  margin-bottom: 0px !important;
}

.faq-header {
  display: grid;
  grid-template-rows: 40% 60%;
  gap: 20px;
  padding: 2vw 9vw 0vw 9vw;
  grid-template-columns: 7fr 3fr;
}
.faq-heading h1 {
  color: #306fbc;
  margin: 0;
}

.faq-desc {
  font-style: italic;
  margin: 0;
  margin-top: -50px;
}

.faq-image img {
  width: 100%;
  max-width: 282px;
}

@media (max-width: 600px) {
  .faq-header {
    display: grid;
    grid-template-columns: 100% !important;
    grid-template-rows: 32% 19% 40% !important;
    row-gap: 5px !important;
  }
  .faq-image {
    grid-row: 1;
  }
  .div-desc {
    grid-column: auto !important;
  }
  .faq-desc {
    margin-top: 0 !important;
  }
  .faq-image img {
    height: 160px;
  }
}
@media (max-width: 1200px) {
  .faq-header {
    display: grid;
    grid-template-columns: 60% 40%;
    grid-template-rows: 45% 40%;
    row-gap: 16px;
  }
  .div-desc {
    grid-column: 1 / span 2;
  }
  .faq-desc {
    margin-top: 0 !important;
  }
}
