<!DOCTYPE html>
<html lang="en">

<!-- Added by HTTrack -->
<meta http-equiv="content-type" content="text/html;charset=ISO-8859-1" /><!-- /Added by HTTrack -->

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<title>About Precium: Who we are?</title>
	<meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,user-scalable=no">
	<meta name="description" content="To know more about Precium & their team members.  " />
	<meta name="keywords" content=" " />
	<link rel="canonical" href="https://precium.in/about-us" />
	<link rel="apple-touch-icon" href="/images/logo-purple.svg" />
    <link rel="icon" href="/images/logo-purple.ico" />
	<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"
		integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo"
		crossorigin="anonymous"></script>
	<script src="https://cdn.jsdelivr.net/npm/popper.js@1.14.3/dist/umd/popper.min.js"
		integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49"
		crossorigin="anonymous"></script>
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.1.3/dist/js/bootstrap.min.js"
		integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy"
		crossorigin="anonymous"></script>
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.3.7/dist/css/bootstrap.min.css"
		integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">

	<link rel="stylesheet" type="text/css" href="/css/style.css">
	<link rel="stylesheet" type="text/css" href="/about-us/style.css">
	<script src="/js/stickUp.min.js"></script>
	<script src="/js/header.js"></script>
	<script src="/js/footer.js"></script>
	<!-- Google Tag Manager -->

	<!-- End Google Tag Manager -->
</head>

<body>
	<!-- Google Tag Manager (noscript) -->
	<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KPC9WJ3" height="0" width="0"
			style="display:none;visibility:hidden"></iframe></noscript>
	<!-- End Google Tag Manager (noscript) -->
	<!-- Header Starts -->
	<my-header></my-header>
	
	<div class="cus-header">
        <div class="cus-heading">
			<h1 class="heading-about-us">About us</h1>
        </div>
		<div class="cus-image">
			<img src="/images/about-us.svg" alt="image">
		</div>
    </div>
	<section class="section-header" style="margin-top: -20px;">
		<div >
			<div class="row-alignment">
				<div class="col-md-12" >
					<div class="section_title" style="margin-top: 0;">
						<!-- <div class="text-center"><img src="images/logo.svg" class="navbar-logo"></div> -->
						<p style="font-size: 20px;"><b><samp
									style="color: #636363; font-weight: 800;">Vision:</samp></b> <br>Support Hotels to Grow Revenues & Profitability</p>

						<p style="font-size: 20px;"><b><samp style="color: #636363; font-weight: 800;">Our
									Mission:</samp></b><br>Utilise Technology to support hotels to maximise Revenues</p>
					</div>
				</div>
				<div class="col-md-12">
					<p style="font-size: 20px;"><strong> <samp
								style="color: #636363; font-weight: 800;">Precium</samp></strong> is an Automated Pricing Recommendation tool. Precium versions are designed to fit in with your requirements.<br /><br />
						Precium clubbed with our Revenue Management service results in significant Revenue growth. We apply Revenue Management Strategy as per the given market conditions and our clients vision and goals. We make decisions based on market insights and real time data, collected from multiple sources. We are a group of experienced Revenue Managers with experience of more than 30 years. Our revenue managers have got insights of the market they handle and are well verse with the technology.</p>
				</div>
				<div class="clearfix"></div>
				<hr>
			</div>

			<div class="row-alignment">
				<div class="section_title text-center">
					<h2 style="color: #636363;">Core Team Introduction</h2>
				</div>

				<div class="col-md-12 team-div">
					<div class="testimonial">
						<div class="pic">
							<img src="/images/kk_img.jpg" class="" alt="KAMAL KISHORRE Image">
						</div>
						<div class="testimonial-content">
							<h3 class="testimonial-title">KAMMELH KISHORRE <small class="post"> Partner </small>
							</h3>
							<p class="description text-justify">

								Kammelh founded the company in 2012. He is an alumnus of IHM Pusa, New Delhi - 1999 Batch
								having over 20 years of experience in Hospitality and Revenue Management in Indian and
								International market. Kammelh has worked with global brands like Forte Group United
								Kingdom and InterContinental Hotels Group United Kingdom.<span id="dots2">..</span><span id="more2" style="display:none ;">
									During his tenure with IHG,
								Kammelh worked in various roles from a single property Revenue Manager to Multi Property
								Revenue Manager. He was given the opportunity to work with IHG Revenue Development and
								Training Team to support their hotels across Europe, Middle East and Africa. He worked
								in the capacity of Cluster Revenue Manager for RMH Team based in the UK supporting
								hotels with Revenue Management.	
									<br /><br />
									In 2009, he moved to India and joined The LaLiT Suri Hospitality group as Corporate
									Revenue Head, Revenue Management and Channel Distribution. Kammelh pioneered a new
									concept in Revenue Management in India to support hotels improve Revenues and
									Profits. Currently he supports over 150 hotels across India and Overseas.
									<br /><br />
									He loves to Watch Air Crash Investigation. Unlike everyone in our team, Kammelh stays
									away
									from food, because he believes, food slows him down. He Eats to Live and Not Live to
									Eat. When he is not working, he is reading, watching or talking about airlines.
									Kammelh can engage with you for hours about commercial airlines.</span>


							</p>
							<!-- <a href="#testimonial2" class="btn btn-primary" data-toggle="collapse">Read More</a> -->
							<button class="btn btn-primary" onclick="myFunction2()" id="myBtn2">Read more</button>
						</div>
					</div>
				</div>

				<div class="col-md-12 team-div">
					<div class="testimonial">
						<div class="pic">
							<img src="/images/rahulsir.jpeg" class="" alt="RAHUL Image">
						</div>
						<div class="testimonial-content">
							<h3 class="testimonial-title">Rahul Aggarwal <small class="post"> Partner</small>
							</h3>
							<p class="description text-justify">
								Rahul Aggarwal is a entrepreneur with more than 12 years of Software development experience. His
								journey started as a small boy who was not brilliant with subjects at school but loved the analytical side 
								of everything. For him, his school, college and jobs were all about analysing the problems and fixing them with coding.<span
									id="dots1">..</span><span id="more1" style="display:none ;">

									<br /><br />
									Completed his B.Tech in Computer Science from IIT Roorkee, he is partner in AxisRooms, HotCocoa Software Pvt Ltd and RemoteX Services LLP.
									Today, his company has helped over 100+ overseas clients with their development needs.  Rahul believe in troubleshooting. He 
									make sure to analyse every problem and work it up. He have very strong interpersonal skills which means - he make connections wherever he goes. 
									His professional scope is all about - Golang, Python, Java, Kotlin, Android, Angular, React, and Javascript development and Kubernetes deployment.
									He hold more than fourteen years of professional experience in software development, which includes co-founding AxisRooms.com (currently being used by more than 20,000 hotels). 



								</span>

							</p>
							<button class="btn btn-primary" onclick="myFunction1()" id="myBtn1">Read more</button>
						</div>
					</div>
				</div>

				<div class="col-md-12 team-div">
					<div class="testimonial">
						<div class="pic">
							<img src="/images/sajalsir.jpeg" class="" alt="SAJAL Image">
						</div>
						<div class="testimonial-content">
							<h3 class="testimonial-title">Sajal Nehra <small class="post"> Partner</small>
							</h3>
							<p class="description text-justify">
								After 3 years of working as a software engineer, Sajal founded Mystral that was a SaaS product to help logistics industry in their vehicles tracking and ERP software for Private Freight terminals. 
								Also, worked closely with Indian Railways to build Real Time Monitoring System and Analytics for the trains. He is also a Co-Founder of RemoteX Services LLP.<span
									id="dots3">..</span><span id="more3" style="display:none ;">

									<br /><br />
									His entrepreneurial instinct has driven him to found 2 startups. RemoteState, a leading company that is in the forefront of developing frontend and backend technology, 
									and Mystral, that provides IOT, tracking, ERP and logistics. He excels in the field of : Product strategy, mobile development, project management, startups, enterprises, and software development processes.

								</span>

							</p>
							<button class="btn btn-primary" onclick="myFunction3()" id="myBtn3">Read more</button>
						</div>
					</div>
				</div>

				<div class="col-md-12 team-div">
					<div class="testimonial">
						<div class="pic">
							<img src="/images/ritiksir.jpeg" class="" alt="RITIK Image">
						</div>
						<div class="testimonial-content">
							<h3 class="testimonial-title">Ritik Rishu <small class="post"> Partner</small>
							</h3>
							<p class="description text-justify">
								Ritik Rishu is a web enthusiast and entrepreneur. He serves as one of the founders @ RemoteX and CEO @ NodeJS LLC. 
								Ritik excelles at Software Architecture and Web Technologies in general. He is very acute at recognizing emerging technologies 
								and deploying them to their best fit real-world use cases. He understands how to bring technical relevance to businesses
								 to optimize and scale their revenue properly.<span
									id="dots4">..</span><span id="more4" style="display:none ;">
										<br><br>
										Ritik is entrepreneurial at the core of this being, and a prolific tech visionary. With his team, he has helped 100+ businesses
										 leverage technology and achieve multi-million dollar evaluations.
										 <br><br>
										 In addition to running multiple businesses and geeking with Web & Mobile, Ritik enjoys being a seasonal angel investor and religious stock trader. 
										 He is passionate about card games and cricket. Addicted to Instagram and memes. Ritik cares a lot about child education and how gender dynamics are 
										 laid out in society. He comes from a "4th tire" place and firmly believes there is an ocean of potential in remote places, both high IQ and hardworking 
										 individuals who are left untapped with the modern pace of knowledge and software revolution. He hopes to bring awareness and thus an opportunity to as many
										  of these 2nd tire, 3rd tire brilliant minds as possible. Always willing to contribute his share in addressing social issues.
								</span>

							</p>
							<button class="btn btn-primary" onclick="myFunction4()" id="myBtn4">Read more</button>
						</div>
					</div>
				</div>
		
			</div>
			
		</div>
		
	</section>

	<!-- footer Starts -->
	<div class="clearfix"></div>
	<my-footer/>

	<script src=/js/stickUp.min.js></script>
	<script type="text/javascript">
		jQuery(function (a) { a(document).ready(function () { a(".navbar-inverse").stickUp() }) });
	</script>
	<script>
		function myFunction1() {
			var dots = document.getElementById("dots1");
			var moreText = document.getElementById("more1");
			var btnText = document.getElementById("myBtn1");

			if (dots.style.display === "none") {
				dots.style.display = "inline";
				btnText.innerHTML = "Read more";
				moreText.style.display = "none";
			} else {
				dots.style.display = "none";
				btnText.innerHTML = "Read less";
				moreText.style.display = "inline";
			}
		}
	</script>
	<script>
		function myFunction2() {
			var dots = document.getElementById("dots2");
			var moreText = document.getElementById("more2");
			var btnText = document.getElementById("myBtn2");

			if (dots.style.display === "none") {
				dots.style.display = "inline";
				btnText.innerHTML = "Read more";
				moreText.style.display = "none";
			} else {
				dots.style.display = "none";
				btnText.innerHTML = "Read less";
				moreText.style.display = "inline";
			}
		}
	</script>
	<script>
		function myFunction3() {
			var dots = document.getElementById("dots3");
			var moreText = document.getElementById("more3");
			var btnText = document.getElementById("myBtn3");

			if (dots.style.display === "none") {
				dots.style.display = "inline";
				btnText.innerHTML = "Read more";
				moreText.style.display = "none";
			} else {
				dots.style.display = "none";
				btnText.innerHTML = "Read less";
				moreText.style.display = "inline";
			}
		}
	</script>
	<script>
		function myFunction4() {
			var dots = document.getElementById("dots4");
			var moreText = document.getElementById("more4");
			var btnText = document.getElementById("myBtn4");

			if (dots.style.display === "none") {
				dots.style.display = "inline";
				btnText.innerHTML = "Read more";
				moreText.style.display = "none";
			} else {
				dots.style.display = "none";
				btnText.innerHTML = "Read less";
				moreText.style.display = "inline";
			}
		}
	</script>
</body>

</html>
