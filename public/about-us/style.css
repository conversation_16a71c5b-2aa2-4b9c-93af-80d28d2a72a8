.section-header {
  font-family: "Poppins";
  font-size: 16px;
}
.cus-header {
  display: grid;
  gap: 20px;
  padding: 2vw 9vw 0vw 9vw;
  grid-template-columns: 7fr 3fr;
}
.cus-heading h1 {
  color: #306fbc;
  margin: 0;
  padding-top: 30px;
}

.cus-desc {
  font-style: italic;
  margin: 0;
  margin-top: -20px;
}

.cus-image img {
  max-height: 248px;
  width: 320px;
}
@media (min-width: 1200px) {
  .cus-heading {
    grid-column: 1;
    grid-row: 1;
  }

  .cus-image {
    grid-column: 2;
    grid-row: 1 / span 2;
  }

  .div-desc {
    grid-column: 1;
    grid-row: 2;
  }
}
@media (max-width: 1200px) {
  .cus-header {
    display: grid;
    grid-template-columns: 60% 40%;
    padding: 2vw 9vw 0vw 9vw;
  }
  .div-desc {
    grid-column: 1 / span 2;
  }
  .cus-desc {
    margin-top: 0 !important;
  }
}

@media (max-width: 600px) {
  .cus-header {
    display: grid;
    grid-template-columns: 100% !important;
    padding: 2vw 9vw 4vw 9vw;
  }
  .cus-heading h1 {
    padding-top: 0;
  }
  .cus-image {
    grid-row: 1;
  }

  .cus-image img {
    height: 250px;
  }
  .autoprice-content {
    padding: 5vw 9vw 6vw;
  }
}
@media (max-width: 400px) {
  .cus-header {
    display: grid;
    grid-template-columns: 100% !important;
    padding: 2vw 9vw 2vw 9vw;
  }
  .cus-image {
    grid-row: 1;
  }
  .cus-image img {
    height: 250px;
  }
}

/* .section-header {
  margin-top: -20px;
} */
