class MyHeader extends HTMLElement {
  constructor() {
    super();
    // Bind methods to maintain context
    this.toggleMobileMenu = this.toggleMobileMenu.bind(this);
    this.toggleDropdown = this.toggleDropdown.bind(this);
    this.handleScroll = this.handleScroll.bind(this);
    this.handleOutsideClick = this.handleOutsideClick.bind(this);
    this.reinitializeEventListeners = this.reinitializeEventListeners.bind(this);
    this.handleResize = this.handleResize.bind(this);

    window.addEventListener("scroll", this.handleScroll);
    window.addEventListener("resize", this.handleResize);
  }

  toggleMobileMenu(e) {

    const menu = this.querySelector("#mobileMenu");
    const isOpen = menu.classList.contains("open");
    
    if (isOpen) {
      this.closeMobileMenu();
    } else {
      this.openMobileMenu();
    }

    e.stopPropagation();
    e.preventDefault();
  }

  openMobileMenu() {
    const menu = this.querySelector("#mobileMenu");

    if (menu) {
      // Use CSS classes for better performance instead of inline styles
      const mobileHeader = this.querySelector(".mobile-header");
      const mobileNavbar = this.querySelector(".mobile-navbar");

      if (mobileHeader && mobileNavbar) {
        // Set CSS custom property for navbar height (more efficient than inline styles)
        document.documentElement.style.setProperty('--navbar-height', mobileNavbar.offsetHeight + 'px');
        mobileHeader.classList.add("mobile-menu-open");
      }

      menu.classList.add("open");

      // Add outside click listener when menu opens (re-enabled for functionality)
      setTimeout(() => {
        document.addEventListener("click", this.handleOutsideClick);
      }, 50); // Reduced timeout for faster response
    }
  }

  closeMobileMenu() {
    const menu = this.querySelector("#mobileMenu");
    const hamImage = this.querySelector("#ham-image");
    const mobileHeader = this.querySelector(".mobile-header");

    if (menu) {
      menu.classList.remove("open");
    }

    if (mobileHeader) {
      // Remove CSS class instead of resetting individual styles
      mobileHeader.classList.remove("mobile-menu-open");
    }

    if (hamImage) {
      hamImage.style.transform = "rotate(0deg)";
    }

    // Remove outside click listener when menu closes
    document.removeEventListener("click", this.handleOutsideClick);
  }

  handleOutsideClick(e) {
    const mobileHeader = this.querySelector(".mobile-header");

    // If click is outside mobile header, close menu
    if (!mobileHeader.contains(e.target)) {
      this.closeMobileMenu();
    }
  }

  toggleDropdown(e) {
    const dropdownParent = e.target.closest(".dropdown-m");

    if (dropdownParent) {
      const link = e.target.closest("a");
      if (link && link.parentElement === dropdownParent) {
        e.preventDefault();

        // Close other dropdowns
        this.querySelectorAll(".dropdown-m").forEach((item) => {
          if (item !== dropdownParent) item.classList.remove("active");
        });

        // Toggle current dropdown
        dropdownParent.classList.toggle("active");

      }
      e.stopPropagation();
    }
  }

  handleScroll() {
    this.closeAllMenus();
  }

  handleResize() {
    // Close menu on resize to avoid positioning issues
    this.closeMobileMenu();
  }

  closeAllMenus() {
    this.closeMobileMenu();
    this.closeAllDropdowns();
  }

  closeAllDropdowns() {
    this.querySelectorAll(".dropdown-m").forEach((item) => {
      item.classList.remove("active");
    });
  }

  // Setup event listeners after DOM is ready
  setupEventListeners() {
    // Remove any existing listeners first
    this.removeEventListeners();

    // Ham menu click handler with delegation for better reliability
    const mobileHeader = this.querySelector(".mobile-header");
    if (mobileHeader) {
      mobileHeader.addEventListener("click", this.handleMobileHeaderClick.bind(this));
    }

    // Dropdown click handlers
    const dropdowns = this.querySelectorAll(".dropdown-m > a");
    dropdowns.forEach(dropdown => {
      dropdown.addEventListener("click", this.toggleDropdown);
    });

    // Mobile menu link clicks (close menu when navigating)
    const mobileLinks = this.querySelectorAll("#mobileMenu a:not(.dropdown-m > a)");
    mobileLinks.forEach(link => {
      link.addEventListener("click", () => {
        this.closeMobileMenu();
      });
    });

    // Style the ham image and mark as having listener
    const hamImage = this.querySelector("#ham-image");
    if (hamImage) {
      hamImage.style.cursor = "pointer";
      hamImage.style.transition = "transform 0.3s ease";
      hamImage.setAttribute('data-listener-attached', 'true');
    }
  }

  // Handle clicks on mobile header with event delegation
  handleMobileHeaderClick(e) {
    if (e.target.id === "ham-image" || e.target.closest("#ham-image")) {
      this.toggleMobileMenu(e);
    }
  }

  // Remove event listeners
  removeEventListeners() {
    const mobileHeader = this.querySelector(".mobile-header");
    if (mobileHeader) {
      mobileHeader.removeEventListener("click", this.handleMobileHeaderClick);
    }

    const dropdowns = this.querySelectorAll(".dropdown-m > a");
    dropdowns.forEach(dropdown => {
      dropdown.removeEventListener("click", this.toggleDropdown);
    });
  }

  // Reinitialize event listeners (useful after DOM changes)
  reinitializeEventListeners() {
    this.setupEventListeners();
  }

  // Cleanup event listeners
  cleanup() {
    this.removeEventListeners();
    document.removeEventListener("click", this.handleOutsideClick);
    window.removeEventListener("scroll", this.handleScroll);
    window.removeEventListener("resize", this.handleResize);
  }

  connectedCallback() {
    // Add Google Analytics script to head if not already present
    const allowedDomains = "https://precium.in";
    if (
      !document.querySelector('script[src*="googletagmanager.com/gtag/js"]') &&
      window.location.origin === allowedDomains
    ) {
      const gtagScript = document.createElement("script");
      gtagScript.async = true;
      gtagScript.src =
        "https://www.googletagmanager.com/gtag/js?id=G-PNFJKKYRD1";
      document.head.appendChild(gtagScript);

      const gtagConfig = document.createElement("script");
      gtagConfig.innerHTML = `
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-PNFJKKYRD1');
      `;
      document.head.appendChild(gtagConfig);
    }

    this.innerHTML = `
        <style>
          

          .mobile-header.mobile-menu-open #mobileMenu {
            position: fixed !important;
            top: var(--navbar-height, 70px) !important;
          }

          /* Smooth transitions for better UX */
          .mobile-header {
            transition: all 0.2s ease;
          }

          #mobileMenu {
            transition: height 0.3s ease;
          }
        </style>
        <div class="content">
				<div class="desktop-header">
				<div class="header-bottom">
					<nav class="navbar  navbar-inverse navtags" >
						<div style="display: flex;
							justify-content: space-around;
							width: 100%;
							flex-wrap: wrap;" class="collapse navbar-collapse navbar-right " id="myNavbar">
								<ul class="nav navbar-nav">
									<li>
										<a  href="/"><img style="padding-right:40px;" id="img_logo" src="/images/logo.svg"
											alt="logo-image" class="navbar-logo" /></a>
									</li>
									<li class="nav-link">
										<a href="/real-time-competitor-rate-shopping-tool" style="background-color: #102D50; border: 2px solid white;font-size: 10px; padding: 8px 10px; border-radius: 10px; color: white; text-decoration: none;  text-align: center;">
										Real - Time Competitor <br> Rate Shopping Tool
										</a>
									</li>
									<li class="nav-link"><a href="/pricing-recommendation" style="background-color: #102D50; border: 2px solid white; font-size: 10px; 
									margin-left: 4px;
									padding: 8px 10px;  border-radius: 10px; color: white; text-decoration: none;  text-align: center;">
										AI -Powered Rate<br> Recommendation Tool
									</a></li>
									<li class="nav-link"><a href="/">Home</a></li>
									<li class="nav-link"><a href="/about-us/">About Us</a></li>
									<li class="dropdown nav-link">
										<a class="dropdown-toggle" href="#">Our Services
											<span class="caret"></span></a>
										<ul class="dropdown-menu">
											<li class="nav-link"><a href="/hotel-revenue-management-audit/">Hotel Revenue Management Audit</a></li>
											<li class="nav-link"><a href="/hotel-revenue-management-training/">Hotel Revenue Growth & Optimisation
												Training</a></li>
											<li class="nav-link"><a href="/revenue-management-support/">Revenue Management Support</a></li>
											<li class="nav-link"><a href="/gds-connectivity/">Global Distribution System (GDS) –
												<br>
												Global Preferred Partners – Sabre Hospitality
												</a></li>
													
										</ul>
									</li>
									<li class="nav-link"><a href="/careers/">Careers</a></li>
									<li class="dropdown nav-link">
										<a class="dropdown-toggle"  href="#">Resources
											<span class="caret"></span></a>
										<ul class="dropdown-menu">
									
											<li class="nav-link"><a href="/faq">FAQ</a></li>
											<li class="nav-link"><a href="/blogs">Blogs</a></li>
											<li class="nav-link"><a href="/our-clients">Our Clients</a></li>
													
										</ul>
									</li>									
									<div class="navbar-sidebutton-con">
										<li class="nav-link">
											<a class="navbar-button2" href="https://app.precium.in/" >Client Login</a>
										</li>
										<li class="nav-link">
											<a href="/contact-us" class="navbar-button2" >Contact Sales</a>
										</li>
									</div>

								</ul>
							</div>
						<!-- </div> -->
					</nav>
				</div>
			
				</div>

				<div class="mobile-header">
					<div  class="mobile-navbar navbar-inverse">
					<a href="/"><img id="img_logo-mobile" src="/images/logo.svg" alt="logo" class="mobile-img" /></a>
					<img src="/images/hamburgur.svg" id="ham-image" alt="hamburgur-image" class="mobile-img" style="cursor: pointer; transition: transform 0.3s ease;">
					</div>

					<div id="mobileMenu">
					<ul class="mobile-list">
						<li class="mobile-list-items"><a href="/real-time-competitor-rate-shopping-tool" style="color: white; text-decoration: none;">Real - Time Competitor <br> Rate Shopping Tool</a></li>
						<li class="mobile-list-items"><a href="/pricing-recommendation">AI -Powered Rate<br> Recommendation Tool</a></li>
						<li class="mobile-list-items"><a href="/">Home</a></li>
						<li class="mobile-list-items"><a href="/about-us">About Us</a></li>
				
						<li class="mobile-list-items dropdown-m">
						<a href="#" style="cursor: pointer;">
							Our Services <span style="font-size: 10px; margin-left: 5px; position: relative; top: -1px;">&#9660;</span>

						</a>
						<ul class="dropdown-menu-m">
							<li class="nav-link"><a href="/hotel-revenue-management-audit/">Hotel Revenue Management Audit</a></li>
							<li class="nav-link"><a href="/hotel-revenue-management-training/">Hotel Revenue Growth & Optimisation Training</a></li>    
							<li class="nav-link"><a href="/revenue-management-support/">Revenue Management Support</a></li> 
							<li class="nav-link"><a href="/gds-connectivity/">Global Distribution System (GDS) –<br>Global Preferred Partners – Sabre Hospitality</a></li> 
						</ul>
						</li>

						<li class="mobile-list-items dropdown-m">
						<a href="#" style="cursor: pointer;">
							Resources  <span style="font-size: 10px; margin-left: 5px; position: relative; top: -1px;">&#9660;</span>
						</a>
						<ul class="dropdown-menu-m">
							<li class="nav-link"><a href="/faq">FAQ</a></li>
							<li class="nav-link"><a href="/blogs">Blogs</a></li>
							<li class="nav-link"><a href="/our-clients">Our Clients</a></li>
						</ul>
						</li>

				
						<li class="mobile-list-items"><a href="/careers">Careers</a></li>
						<li class="mobile-list-items"><a href="https://app.precium.in/">Client Login</a></li>
						<li class="mobile-list-items"><a href="/contact-us">Contact Sales</a></li>
					</ul>
					</div>
	 			 </div>
		</div>
      `;

    // Setup event listeners after DOM is rendered
    setTimeout(() => {
      this.setupEventListeners();

      // Set up mutation observer to handle dynamic changes
      this.setupMutationObserver();

      // Add periodic check to ensure event listeners are working
      this.startPeriodicCheck();
    }, 0);
  }

  // Optimized mutation observer with throttling
  setupMutationObserver() {
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
    }

    let throttleTimeout;
    this.mutationObserver = new MutationObserver((mutations) => {
      // Throttle the reinitialize calls for better performance
      if (throttleTimeout) return;

      let shouldReinit = false;

      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' &&
            mutation.attributeName === 'class' &&
            mutation.target.classList.contains('navbar-inverse')) {
          shouldReinit = true;
        }
      });

      if (shouldReinit) {
        throttleTimeout = setTimeout(() => {
          this.reinitializeEventListeners();
          throttleTimeout = null;
        }, 200); // Increased delay for better performance
      }
    });

    const mobileHeader = this.querySelector(".mobile-header");
    if (mobileHeader) {
      this.mutationObserver.observe(mobileHeader, {
        attributes: true,
        subtree: false, // Reduced scope for better performance
        attributeFilter: ['class'] // Only watch class changes
      });
    }
  }

  // Optimized periodic check with longer interval
  startPeriodicCheck() {
    if (this.periodicCheckInterval) {
      clearInterval(this.periodicCheckInterval);
    }

    this.periodicCheckInterval = setInterval(() => {
      const hamImage = this.querySelector("#ham-image");
      if (hamImage && !hamImage.hasAttribute('data-listener-attached')) {
        this.reinitializeEventListeners();
        hamImage.setAttribute('data-listener-attached', 'true');
      }
    }, 5000); // Increased interval for better performance
  }

  // Cleanup when element is removed from DOM
  disconnectedCallback() {
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
    }

    if (this.periodicCheckInterval) {
      clearInterval(this.periodicCheckInterval);
    }

    this.cleanup();
  }
}

customElements.define("my-header", MyHeader);
