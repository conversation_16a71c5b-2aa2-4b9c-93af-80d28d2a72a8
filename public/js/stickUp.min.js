$(function() {
  const header = $('.navbar-inverse');
  const imgTagLogo = $('#img_logo');
  const imgmobile = $('#img_logo-mobile');
    function getImagePath() {
    const path = window.location.pathname;
        if (path === '/public/') {
      return 'images/';
    }
    return '../images/';
  }

  $(window).scroll(function() {
    const scroll = $(window).scrollTop();
    const imagePath = getImagePath();
    
    
    if (scroll >= 20) {

      imgTagLogo.attr('src', imagePath + 'logo_2.svg');
      header.addClass('isStuck').removeClass('clearHeader');
      
      imgmobile.attr('src', imagePath + 'logo_2.svg');
    } else {
      imgmobile.attr('src', imagePath + 'logo.svg');
      imgTagLogo.attr('src', imagePath + 'logo.svg');
      header.removeClass('isStuck').addClass('clearHeader');
    }
  });
});
