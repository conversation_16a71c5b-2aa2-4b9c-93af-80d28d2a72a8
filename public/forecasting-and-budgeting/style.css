.cus-header {
  display: grid;
  gap: 20px;
  padding: 2vw 9vw 1vw 9vw;
  grid-template-columns: 7fr 3fr;
}
.cus-header h1 {
  color: #306fbc;
  margin: 0;
  padding-top: 15px;
}

.cus-desc {
  font-style: italic;
  margin: 0;
}
.buttons-fix-heights {
  max-height: 58px !important;
}

.cus-image img {
  width: 342px;
  max-height: 248px;
}
@media (min-width: 1200px) {
  .cus-heading {
    grid-column: 1;
    grid-row: 1;
  }

  .cus-image {
    grid-column: 2;
    grid-row: 1 / span 2;
  }

  .div-desc {
    grid-column: 1;
    grid-row: 2;
  }
}
@media (max-width: 1200px) {
  .cus-header {
    display: grid;
    grid-template-columns: 60% 40%;
    grid-template-rows: 62% 26%;
    row-gap: 16px;
    padding: 2vw 9vw 4vw 9vw;
  }
  .div-desc {
    grid-column: 1 / span 2;
  }
  .cus-desc {
    margin-top: 0 !important;
  }
}

@media (max-width: 600px) {
  .cus-header {
    display: grid;
    grid-template-columns: 100% !important;
    padding: 2vw 9vw 2vw 9vw;
    grid-template-rows: 42% 36% 24% !important;
    row-gap: 5px !important;
  }
  .cus-header h1 {
    padding-top: 0%;
  }
  .cus-image {
    grid-row: 1;
  }
  .div-desc {
    grid-column: auto !important;
  }
  .cus-desc {
    margin-top: 0 !important;
  }
  .cus-image img {
    height: 250px;
  }
}
@media (max-width: 430px) {
  .cus-header {
    display: grid;
    grid-template-columns: 100% !important;
    padding: 2vw 9vw 2vw 9vw;
    grid-template-rows: 40% 40% 22% !important;
    row-gap: 5px !important;
  }
  .cus-header h1 {
    padding-top: 0%;
  }
  .cus-image {
    grid-row: 1;
  }
  .div-desc {
    grid-column: auto !important;
  }
  .cus-desc {
    margin-top: 0 !important;
  }
  .cus-image img {
    height: 250px;
  }
}
